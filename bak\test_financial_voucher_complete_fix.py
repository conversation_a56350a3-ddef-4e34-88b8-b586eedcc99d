#!/usr/bin/env python3
"""
测试完整的财务凭证生成修复
验证供应商信息、详细摘要、正确科目编码等功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import *
from app.models_financial import *
from sqlalchemy import text
from decimal import Decimal

def test_complete_voucher_generation():
    """测试完整的财务凭证生成逻辑"""
    app = create_app()
    
    with app.app_context():
        print("=== 测试完整的财务凭证生成逻辑 ===")
        
        # 1. 检查关键会计科目
        print("\n1. 检查关键会计科目:")
        key_subjects = {
            '1201': '原材料总科目',
            '120101': '蔬菜类',
            '120102': '肉类',
            '120103': '水产类',
            '120104': '粮油类',
            '120105': '调料类',
            '120106': '冷冻食品',
            '2001': '应付账款',
            '2201': '应交税费'
        }
        
        for code, desc in key_subjects.items():
            subject = AccountingSubject.query.filter_by(code=code).first()
            if subject:
                print(f"  ✓ {code}: {subject.name} ({desc})")
            else:
                print(f"  ✗ {code}: 未找到 ({desc})")
        
        # 2. 查找有供应商信息的入库单
        print("\n2. 查找有供应商信息的入库单:")
        stock_in_with_supplier = db.session.execute(text("""
            SELECT TOP 5 
                si.id, 
                si.stock_in_number, 
                si.total_cost,
                s.name as supplier_name,
                COUNT(sii.id) as item_count
            FROM stock_ins si
            LEFT JOIN suppliers s ON si.supplier_id = s.id
            LEFT JOIN stock_in_items sii ON si.id = sii.stock_in_id
            WHERE si.total_cost > 0
            GROUP BY si.id, si.stock_in_number, si.total_cost, s.name
            HAVING COUNT(sii.id) > 0
            ORDER BY si.id DESC
        """)).fetchall()
        
        for row in stock_in_with_supplier:
            supplier_name = row.supplier_name or '未知供应商'
            print(f"  - 入库单 {row.stock_in_number}: {supplier_name}, 金额: {row.total_cost}, 明细: {row.item_count}项")
        
        # 3. 分析第一个入库单的详细信息
        if stock_in_with_supplier:
            test_stock_in = stock_in_with_supplier[0]
            print(f"\n3. 分析入库单 {test_stock_in.stock_in_number} 的详细信息:")
            
            # 查询食材分类详情
            category_details = db.session.execute(text("""
                SELECT
                    ic.name as category_name,
                    SUM(CAST(sii.quantity AS DECIMAL(10,2)) * CAST(sii.unit_price AS DECIMAL(10,2))) as category_total_cost,
                    SUM(CAST(sii.quantity AS DECIMAL(10,2))) as total_quantity,
                    STRING_AGG(i.name + '(' + CAST(sii.quantity AS VARCHAR(20)) + sii.unit + ')', '、') as ingredients_detail
                FROM stock_in_items sii
                JOIN ingredients i ON sii.ingredient_id = i.id
                LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
                WHERE sii.stock_in_id = :stock_in_id
                GROUP BY ic.name
            """), {'stock_in_id': test_stock_in.id}).fetchall()
            
            # 食材分类映射
            category_to_code_map = {
                '蔬菜类': '120101', '蔬菜': '120101',
                '肉类': '120102', '肉': '120102',
                '水产类': '120103', '水产': '120103',
                '粮油类': '120104', '粮油': '120104',
                '调料类': '120105', '调料': '120105', '调味品': '120105',
                '冷冻食品': '120106', '冷冻': '120106',
                '其他': '1201'
            }
            
            total_check = 0
            for row in category_details:
                category_name = row.category_name or '其他'
                category_total = float(row.category_total_cost or 0)
                ingredients_detail = row.ingredients_detail or ''
                
                # 获取对应的科目编码
                subject_code = category_to_code_map.get(category_name, '1201')
                subject = AccountingSubject.query.filter_by(code=subject_code).first()
                
                # 生成摘要
                supplier_name = test_stock_in.supplier_name or '未知供应商'
                summary = f'从{supplier_name}购买{category_name}：{ingredients_detail}'
                if len(summary) > 100:
                    summary = f'从{supplier_name}购买{category_name}等食材'
                
                print(f"  分类: {category_name}")
                print(f"    科目: {subject_code} - {subject.name if subject else '未找到'}")
                print(f"    金额: {category_total:.2f}")
                print(f"    摘要: {summary}")
                print(f"    明细: {ingredients_detail}")
                print()
                
                total_check += category_total
            
            # 应付账款明细
            payable_subject = AccountingSubject.query.filter_by(code='2001').first()
            payable_summary = f'应付{supplier_name}货款-入库单{test_stock_in.stock_in_number}'
            
            print(f"  应付账款:")
            print(f"    科目: 2001 - {payable_subject.name if payable_subject else '未找到'}")
            print(f"    金额: {float(test_stock_in.total_cost):.2f}")
            print(f"    摘要: {payable_summary}")
            
            print(f"\n  金额校验: {total_check:.2f} vs {float(test_stock_in.total_cost):.2f}")
            if abs(total_check - float(test_stock_in.total_cost)) < 0.01:
                print("  ✓ 金额校验通过")
            else:
                print("  ✗ 金额校验失败")

def test_subject_mapping_completeness():
    """测试科目映射的完整性"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 测试科目映射完整性 ===")
        
        # 检查所有食材分类是否都有对应的科目
        categories = IngredientCategory.query.all()
        category_to_code_map = {
            '蔬菜类': '120101', '蔬菜': '120101',
            '肉类': '120102', '肉': '120102',
            '水产类': '120103', '水产': '120103',
            '粮油类': '120104', '粮油': '120104',
            '调料类': '120105', '调料': '120105', '调味品': '120105',
            '冷冻食品': '120106', '冷冻': '120106'
        }
        
        print("\n食材分类与科目映射检查:")
        for category in categories:
            subject_code = category_to_code_map.get(category.name, '1201')
            subject = AccountingSubject.query.filter_by(code=subject_code).first()
            
            if subject:
                print(f"  ✓ {category.name} -> {subject_code}: {subject.name}")
            else:
                print(f"  ✗ {category.name} -> {subject_code}: 科目不存在")

if __name__ == '__main__':
    print("开始测试完整的财务凭证修复...")
    test_complete_voucher_generation()
    test_subject_mapping_completeness()
    print("\n测试完成!")
