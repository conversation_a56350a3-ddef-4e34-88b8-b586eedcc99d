#!/usr/bin/env python3
"""
测试自动日志创建功能
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User
from app.services.auto_daily_log_service import AutoDailyLogService
from datetime import date

def test_auto_log_creation():
    """测试自动日志创建功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 测试自动日志创建功能 ===")
        
        # 获取一个测试用户
        user = User.query.filter_by(username='guest_demo').first()
        if not user:
            print("❌ 未找到测试用户 guest_demo")
            return
        
        print(f"✅ 找到测试用户: {user.username}")
        
        # 获取用户关联的学校
        user_area = user.get_current_area()
        if not user_area:
            print("❌ 用户没有关联的学校")
            return
        
        print(f"✅ 用户关联学校: {user_area.name}")
        
        # 测试自动创建今天的日志
        print(f"\n--- 测试为用户创建今天({date.today()})的日志 ---")
        success, message, log = AutoDailyLogService.auto_create_today_log(user)
        
        print(f"结果: {'✅ 成功' if success else '❌ 失败'}")
        print(f"消息: {message}")
        if log:
            print(f"日志ID: {log.id}")
            print(f"日志日期: {log.log_date}")
            print(f"管理员: {log.manager}")
            print(f"创建时间: {log.created_at}")
        
        # 测试批量创建功能
        print(f"\n--- 测试批量创建所有学校的今日日志 ---")
        result = AutoDailyLogService.auto_create_logs_for_all_areas()
        
        print(f"总学校数: {result['total_areas']}")
        print(f"新建日志: {result['created_count']}")
        print(f"已存在日志: {result['existing_count']}")
        print(f"失败数量: {result['failed_count']}")
        
        if result.get('error'):
            print(f"❌ 错误: {result['error']}")
        
        # 显示详细结果
        print("\n详细结果:")
        for item in result['results'][:5]:  # 只显示前5个
            status_icon = "✅" if item['status'] == 'created' else "ℹ️" if item['status'] == 'existing' else "❌"
            print(f"  {status_icon} {item['area_name']}: {item['message']}")
        
        if len(result['results']) > 5:
            print(f"  ... 还有 {len(result['results']) - 5} 个学校")
        
        # 测试检查缺失日志功能
        print(f"\n--- 测试检查缺失日志功能 ---")
        missing_result = AutoDailyLogService.check_and_create_missing_logs(days_back=3)
        
        print(f"检查天数: {missing_result['days_checked']}")
        print(f"检查学校数: {missing_result['areas_checked']}")
        print(f"新建日志: {missing_result['created_count']}")
        print(f"已存在日志: {missing_result['existing_count']}")
        
        if missing_result.get('error'):
            print(f"❌ 错误: {missing_result['error']}")
        
        print("\n=== 测试完成 ===")

if __name__ == '__main__':
    test_auto_log_creation()
