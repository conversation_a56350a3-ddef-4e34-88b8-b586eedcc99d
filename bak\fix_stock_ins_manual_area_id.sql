-- 修复 stock_ins 表的财务字段（手动设置 area_id）
-- 请在 SQL Server Management Studio 中执行此脚本

USE [StudentsCMSSP]
GO

PRINT '=== 第一步：添加基本字段 ==='

-- 添加 area_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'area_id')
BEGIN
    ALTER TABLE stock_ins ADD area_id INT NULL;
    PRINT '✓ 添加 area_id 字段'
END
ELSE
BEGIN
    PRINT '- area_id 字段已存在'
END

-- 添加 supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_ins ADD supplier_id INT NULL;
    PRINT '✓ 添加 supplier_id 字段'
END
ELSE
BEGIN
    PRINT '- supplier_id 字段已存在'
END

-- 添加 payable_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'payable_id')
BEGIN
    ALTER TABLE stock_ins ADD payable_id INT NULL;
    PRINT '✓ 添加 payable_id 字段'
END
ELSE
BEGIN
    PRINT '- payable_id 字段已存在'
END

-- 添加 voucher_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'voucher_id')
BEGIN
    ALTER TABLE stock_ins ADD voucher_id INT NULL;
    PRINT '✓ 添加 voucher_id 字段'
END
ELSE
BEGIN
    PRINT '- voucher_id 字段已存在'
END

-- 添加 is_financial_confirmed 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'is_financial_confirmed')
BEGIN
    ALTER TABLE stock_ins ADD is_financial_confirmed BIT NOT NULL DEFAULT 0;
    PRINT '✓ 添加 is_financial_confirmed 字段'
END
ELSE
BEGIN
    PRINT '- is_financial_confirmed 字段已存在'
END

-- 添加 total_cost 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'total_cost')
BEGIN
    ALTER TABLE stock_ins ADD total_cost DECIMAL(10,2) NULL;
    PRINT '✓ 添加 total_cost 字段'
END
ELSE
BEGIN
    PRINT '- total_cost 字段已存在'
END

-- 添加 financial_confirmed_at 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_at')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_at DATETIME2(1) NULL;
    PRINT '✓ 添加 financial_confirmed_at 字段'
END
ELSE
BEGIN
    PRINT '- financial_confirmed_at 字段已存在'
END

-- 添加 financial_confirmed_by 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_by')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_by INT NULL;
    PRINT '✓ 添加 financial_confirmed_by 字段'
END
ELSE
BEGIN
    PRINT '- financial_confirmed_by 字段已存在'
END

PRINT '=== 第一步完成 ==='
PRINT ''

PRINT '=== 第二步：手动设置 area_id ==='

-- 手动设置所有入库单的 area_id 为 42（您当前学校的ID）
UPDATE stock_ins 
SET area_id = 42 
WHERE area_id IS NULL;

DECLARE @updated_count INT = @@ROWCOUNT;
PRINT '✓ 更新了 ' + CAST(@updated_count AS VARCHAR(10)) + ' 条记录的 area_id'
PRINT '=== 第二步完成 ==='

PRINT ''
PRINT '=== 第三步：计算 total_cost ==='

-- 为现有数据计算 total_cost（从入库明细计算）
UPDATE si
SET total_cost = ISNULL((
    SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
), 0)
WHERE si.total_cost IS NULL OR si.total_cost = 0;

DECLARE @cost_updated INT = @@ROWCOUNT;
PRINT '✓ 计算了 ' + CAST(@cost_updated AS VARCHAR(10)) + ' 条记录的 total_cost'
PRINT '=== 第三步完成 ==='

PRINT ''
PRINT '=== 第四步：设置 supplier_id ==='

-- 为现有数据设置 supplier_id（从第一个入库明细获取）
UPDATE si
SET supplier_id = (
    SELECT TOP 1 sii.supplier_id
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
    AND sii.supplier_id IS NOT NULL
)
WHERE si.supplier_id IS NULL;

DECLARE @supplier_updated INT = @@ROWCOUNT;
PRINT '✓ 设置了 ' + CAST(@supplier_updated AS VARCHAR(10)) + ' 条记录的 supplier_id'
PRINT '=== 第四步完成 ==='

PRINT ''
PRINT '=== 第五步：创建索引 ==='

-- 创建索引以提高查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_is_financial_confirmed')
BEGIN
    CREATE INDEX IX_stock_ins_is_financial_confirmed ON stock_ins(is_financial_confirmed);
    PRINT '✓ 创建 is_financial_confirmed 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_supplier_id')
BEGIN
    CREATE INDEX IX_stock_ins_supplier_id ON stock_ins(supplier_id);
    PRINT '✓ 创建 supplier_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_area_id')
BEGIN
    CREATE INDEX IX_stock_ins_area_id ON stock_ins(area_id);
    PRINT '✓ 创建 area_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_payable_id')
BEGIN
    CREATE INDEX IX_stock_ins_payable_id ON stock_ins(payable_id);
    PRINT '✓ 创建 payable_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_voucher_id')
BEGIN
    CREATE INDEX IX_stock_ins_voucher_id ON stock_ins(voucher_id);
    PRINT '✓ 创建 voucher_id 索引'
END

PRINT '=== 第五步完成 ==='

PRINT ''
PRINT '=== 验证修复结果 ==='

-- 显示修复后的统计信息
SELECT 
    COUNT(*) as 入库单总数,
    COUNT(area_id) as 有area_id的记录数,
    COUNT(supplier_id) as 有supplier_id的记录数,
    COUNT(total_cost) as 有total_cost的记录数,
    SUM(CASE WHEN is_financial_confirmed = 1 THEN 1 ELSE 0 END) as 已财务确认数量
FROM stock_ins;

PRINT ''
PRINT '🎉 stock_ins 表基本修复完成！'
PRINT '现在可以测试财务模块了。'
PRINT ''
PRINT '如果需要添加外键约束，请执行 add_foreign_keys.sql'

GO
