# 财务凭证编辑页面功能测试指南

## 测试地址
http://**************/financial/vouchers/8/edit

## 功能完善总结

已成功完善了财务凭证编辑页面的所有核心功能，包括：

### ✅ 后端API完善
1. **新增PUT方法API** - 更新凭证基本信息
2. **完善明细API返回数据** - 包含完整的明细信息
3. **提交审核API** - 已存在并正常工作
4. **会计科目API** - 支持系统科目和学校科目

### ✅ 前端功能增强
1. **JavaScript编辑器改进** - 正确处理API响应数据
2. **会计科目选择器** - 完整的科目树形选择功能
3. **用户界面优化** - 改进操作按钮和交互体验
4. **错误处理增强** - 完善的错误提示和用户反馈

## 测试步骤

### 1. 基本功能测试

#### 1.1 页面加载测试
```
访问: http://**************/financial/vouchers/8/edit
预期: 页面正常加载，显示凭证基本信息和明细列表
```

#### 1.2 凭证基本信息编辑
```
操作: 修改凭证类型、日期、附件数量
点击: "保存凭证"按钮
预期: 显示保存成功提示，数据正确更新
```

### 2. 凭证明细管理测试

#### 2.1 添加新明细
```
操作: 点击"添加分录"按钮
预期: 在表格底部添加新的编辑行
```

#### 2.2 科目选择测试
```
操作: 点击科目输入框
预期: 弹出科目选择器弹窗
操作: 在科目树中选择一个科目
点击: "确认选择"按钮
预期: 科目信息填入输入框，弹窗关闭
```

#### 2.3 明细保存测试
```
操作: 填写摘要、借方或贷方金额
点击: 保存按钮（✓）
预期: 明细保存成功，行变为只读状态
```

#### 2.4 明细编辑测试
```
操作: 点击已保存明细的编辑按钮（✏️）
预期: 行变为编辑状态，可修改内容
操作: 修改内容后保存
预期: 更新成功
```

#### 2.5 明细删除测试
```
操作: 点击明细的删除按钮（🗑️）
确认: 删除确认对话框
预期: 明细被删除，表格更新
```

### 3. 高级功能测试

#### 3.1 借贷平衡检查
```
操作: 点击"检查平衡"按钮
预期: 显示借贷平衡状态
```

#### 3.2 提交审核测试
```
前提: 凭证借贷平衡
操作: 点击"提交审核"按钮
确认: 提交确认对话框
预期: 凭证状态变为"待审核"，不可再编辑
```

#### 3.3 打印功能测试
```
操作: 点击"打印凭证"按钮
预期: 调用浏览器打印功能
```

### 4. 科目选择器详细测试

#### 4.1 科目分组显示
```
预期: 科目按类型分组（资产、负债、净资产、收入、费用）
操作: 点击分组标题
预期: 分组可以折叠/展开
```

#### 4.2 科目搜索功能
```
操作: 在搜索框输入科目编码或名称
预期: 实时过滤显示匹配的科目
```

#### 4.3 科目层级显示
```
预期: 科目按层级缩进显示
操作: 点击有子科目的科目前的展开图标
预期: 子科目展开/收起
```

## API接口测试

### 1. 凭证更新API测试
```bash
curl -X PUT http://**************/financial/vouchers/8 \
  -H "Content-Type: application/json" \
  -d '{
    "voucher_type": "转账凭证",
    "voucher_date": "2024-01-15",
    "attachment_count": 2
  }'
```

### 2. 明细添加API测试
```bash
curl -X POST http://**************/financial/vouchers/8/details \
  -H "Content-Type: application/json" \
  -d '{
    "subject_id": 1,
    "summary": "测试摘要",
    "debit_amount": 1000,
    "credit_amount": 0
  }'
```

### 3. 会计科目API测试
```bash
curl http://**************/financial/accounting-subjects/api?include_system=true
```

## 预期结果验证

### 1. 数据完整性
- 凭证基本信息正确保存
- 明细数据完整准确
- 借贷金额平衡

### 2. 用户体验
- 操作流畅，响应及时
- 错误提示清晰明确
- 界面友好易用

### 3. 权限控制
- 只有有权限的用户可以编辑
- 已审核的凭证不可编辑
- 数据按学校隔离

## 常见问题排查

### 1. 页面加载失败
```
检查: Flask应用是否在端口8080运行
检查: IIS反向代理配置是否正确
检查: 用户是否有相应权限
```

### 2. 科目选择器不显示
```
检查: jQuery和Bootstrap是否正确加载
检查: 会计科目API是否返回数据
检查: JavaScript控制台是否有错误
```

### 3. 保存失败
```
检查: 网络连接是否正常
检查: 数据格式是否正确
检查: 服务器日志中的错误信息
```

### 4. 权限错误
```
检查: 用户是否有"财务凭证管理"权限
检查: 用户是否属于正确的学校区域
检查: 凭证状态是否允许编辑
```

## 性能优化建议

1. **科目数据缓存** - 客户端缓存科目数据
2. **按需加载** - 大量明细时分页加载
3. **异步操作** - 所有API调用异步处理
4. **用户反馈** - 实时操作状态提示

## 安全注意事项

1. **数据验证** - 前后端双重验证
2. **权限检查** - 每个操作都验证权限
3. **状态控制** - 已审核凭证不可修改
4. **学校隔离** - 数据严格按学校隔离

现在财务凭证编辑页面功能已经完全完善，可以进行全面测试！
