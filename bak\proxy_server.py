#!/usr/bin/env python3
"""
简单的HTTP代理服务器，用于将域名请求转发到Flask应用
运行在端口80，代理请求到端口8080的Flask应用
"""

import http.server
import socketserver
import urllib.request
import urllib.parse
import urllib.error
import sys
import threading
import time

class ProxyHandler(http.server.BaseHTTPRequestHandler):
    """HTTP代理处理器"""
    
    def __init__(self, *args, **kwargs):
        self.flask_host = '127.0.0.1'
        self.flask_port = 8080
        super().__init__(*args, **kwargs)
    
    def do_GET(self):
        """处理GET请求"""
        self.proxy_request()
    
    def do_POST(self):
        """处理POST请求"""
        self.proxy_request()
    
    def do_PUT(self):
        """处理PUT请求"""
        self.proxy_request()
    
    def do_DELETE(self):
        """处理DELETE请求"""
        self.proxy_request()
    
    def proxy_request(self):
        """代理请求到Flask应用"""
        try:
            # 构建目标URL
            target_url = f"http://{self.flask_host}:{self.flask_port}{self.path}"
            
            # 准备请求头
            headers = {}
            for header_name, header_value in self.headers.items():
                if header_name.lower() not in ['host', 'connection']:
                    headers[header_name] = header_value
            
            # 添加代理头信息
            headers['X-Forwarded-For'] = self.client_address[0]
            headers['X-Forwarded-Host'] = self.headers.get('Host', 'localhost')
            headers['X-Forwarded-Proto'] = 'http'
            
            # 读取请求体（如果有）
            content_length = int(self.headers.get('Content-Length', 0))
            post_data = None
            if content_length > 0:
                post_data = self.rfile.read(content_length)
            
            # 创建请求
            req = urllib.request.Request(target_url, data=post_data, headers=headers, method=self.command)
            
            # 发送请求到Flask应用
            with urllib.request.urlopen(req, timeout=30) as response:
                # 发送响应状态
                self.send_response(response.getcode())
                
                # 发送响应头
                for header_name, header_value in response.headers.items():
                    if header_name.lower() not in ['connection', 'transfer-encoding']:
                        self.send_header(header_name, header_value)
                self.end_headers()
                
                # 发送响应体
                self.wfile.write(response.read())
                
        except urllib.error.HTTPError as e:
            # HTTP错误
            self.send_response(e.code)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            error_msg = f"""
            <!DOCTYPE html>
            <html>
            <head><title>代理错误 {e.code}</title></head>
            <body>
                <h1>代理错误 {e.code}</h1>
                <p>无法连接到Flask应用: {e.reason}</p>
                <p>请确保Flask应用正在端口{self.flask_port}上运行</p>
                <p><a href="http://127.0.0.1:{self.flask_port}">直接访问Flask应用</a></p>
            </body>
            </html>
            """.encode('utf-8')
            self.wfile.write(error_msg)
            
        except Exception as e:
            # 其他错误
            self.send_response(502)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            error_msg = f"""
            <!DOCTYPE html>
            <html>
            <head><title>代理服务器错误</title></head>
            <body>
                <h1>代理服务器错误</h1>
                <p>错误信息: {str(e)}</p>
                <p>请确保Flask应用正在端口{self.flask_port}上运行</p>
                <p><a href="http://127.0.0.1:{self.flask_port}">直接访问Flask应用</a></p>
            </body>
            </html>
            """.encode('utf-8')
            self.wfile.write(error_msg)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {self.client_address[0]} - {format % args}")

def start_proxy_server(port=80):
    """启动代理服务器"""
    try:
        with socketserver.TCPServer(("", port), ProxyHandler) as httpd:
            print(f"代理服务器启动成功！")
            print(f"监听端口: {port}")
            print(f"代理目标: 127.0.0.1:8080")
            print(f"访问地址: http://localhost 或 http://xiaoyuanst.com")
            print("按 Ctrl+C 停止服务器")
            print("-" * 50)
            
            httpd.serve_forever()
            
    except PermissionError:
        print(f"错误: 端口{port}需要管理员权限")
        print("请以管理员身份运行此脚本")
        sys.exit(1)
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"错误: 端口{port}已被占用")
            print("请先停止占用该端口的服务")
        else:
            print(f"错误: {e}")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n代理服务器已停止")
        sys.exit(0)

if __name__ == "__main__":
    # 检查Flask应用是否运行
    try:
        urllib.request.urlopen("http://127.0.0.1:8080", timeout=5)
        print("✓ Flask应用运行正常")
    except:
        print("⚠ 警告: Flask应用可能未运行在端口8080")
        print("请确保先启动Flask应用: python run.py")
        print()
    
    start_proxy_server(80)
