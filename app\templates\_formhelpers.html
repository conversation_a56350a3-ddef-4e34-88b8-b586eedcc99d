{% macro render_field(field) %}
  <div class="form-group">
    {{ field.label }}
    {{ field(**kwargs)|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_checkbox(field) %}
  <div class="form-group form-check">
    {{ field(class="form-check-input")|safe }}
    {{ field.label(class="form-check-label") }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_radio(field) %}
  <div class="form-group">
    {{ field.label }}
    <div>
      {% for subfield in field %}
        <div class="form-check form-check-inline">
          {{ subfield(class="form-check-input")|safe }}
          {{ subfield.label(class="form-check-label") }}
        </div>
      {% endfor %}
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_select(field) %}
  <div class="form-group">
    {{ field.label }}
    {{ field(class="form-control")|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_textarea(field) %}
  <div class="form-group">
    {{ field.label }}
    {{ field(class="form-control", rows=5)|safe }}
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_date(field) %}
  <div class="form-group">
    {{ field.label }}
    <div class="input-group date" id="{{ field.id }}_datepicker">
      {{ field(class="form-control datepicker")|safe }}
      <div class="input-group-append">
        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
      </div>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_datetime(field) %}
  <div class="form-group">
    {{ field.label }}
    <div class="input-group date" id="{{ field.id }}_datetimepicker">
      {{ field(class="form-control datetimepicker")|safe }}
      <div class="input-group-append">
        <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
      </div>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

{% macro render_file(field) %}
  <div class="form-group">
    {{ field.label }}
    <div class="custom-file">
      {{ field(class="custom-file-input")|safe }}
      <label class="custom-file-label" for="{{ field.id }}">选择文件</label>
    </div>
    {% if field.errors %}
      <div class="invalid-feedback" style="display: block;">
        {% for error in field.errors %}
          <span>{{ error }}</span>
        {% endfor %}
      </div>
    {% endif %}
  </div>
{% endmacro %}

<!-- 通用布局宏 -->

<!-- 标准页面容器 -->
{% macro page_container(fluid=true) %}
<div class="{% if fluid %}container-fluid{% else %}container{% endif %}">
    {{ caller() }}
</div>
{% endmacro %}

<!-- 标准卡片布局 -->
{% macro card_layout(title, subtitle=none, tools=none, class="") %}
<div class="row">
    <div class="col-12">
        <div class="card {{ class }}">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h3 class="card-title mb-0">{{ title }}</h3>
                        {% if subtitle %}
                        <small class="text-muted">{{ subtitle }}</small>
                        {% endif %}
                    </div>
                    {% if tools %}
                    <div class="card-tools">
                        {{ tools }}
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="card-body">
                {{ caller() }}
            </div>
        </div>
    </div>
</div>
{% endmacro %}

<!-- 搜索表单布局 -->
{% macro search_form(action_url, method="get") %}
<form method="{{ method }}" action="{{ action_url }}" class="mb-4">
    <div class="row">
        {{ caller() }}
        <div class="col-md-2">
            <div class="form-group">
                <label>&nbsp;</label>
                <button type="submit" class="btn btn-primary btn-block">搜索</button>
            </div>
        </div>
    </div>
</form>
{% endmacro %}

<!-- 表格布局 -->
{% macro table_layout(headers, responsive=true) %}
<div class="{% if responsive %}table-responsive{% endif %}">
    <table class="table table-bordered table-striped">
        <thead>
            <tr>
                {% for header in headers %}
                <th>{{ header }}</th>
                {% endfor %}
            </tr>
        </thead>
        <tbody>
            {{ caller() }}
        </tbody>
    </table>
</div>
{% endmacro %}

<!-- 状态徽章 -->
{% macro status_badge(status, mapping=none) %}
{% set default_mapping = {
    '正常': 'success',
    '维护中': 'warning',
    '已关闭': 'danger',
    '启用': 'success',
    '禁用': 'secondary',
    '待审核': 'warning',
    '已审核': 'success',
    '已拒绝': 'danger'
} %}
{% set badge_mapping = mapping or default_mapping %}
{% set badge_type = badge_mapping.get(status, 'secondary') %}
<span class="badge badge-{{ badge_type }}">{{ status }}</span>
{% endmacro %}

<!-- 操作按钮组 -->
{% macro action_buttons(items) %}
<div class="btn-group" role="group">
    {% for item in items %}
    <a href="{{ item.url }}" class="btn btn-{{ item.type|default('primary') }} btn-sm"
       {% if item.title %}title="{{ item.title }}"{% endif %}
       {% if item.onclick %}onclick="{{ item.onclick }}"{% endif %}>
        {% if item.icon %}<i class="{{ item.icon }}"></i>{% endif %}
        {{ item.text }}
    </a>
    {% endfor %}
</div>
{% endmacro %}

<!-- 空状态提示 -->
{% macro empty_state(message="暂无数据", icon="fas fa-inbox", action_text=none, action_url=none) %}
<div class="text-center py-4">
    <i class="{{ icon }} fa-3x text-muted mb-3"></i>
    <p class="text-muted mb-0">{{ message }}</p>
    {% if action_text and action_url %}
    <a href="{{ action_url }}" class="btn btn-primary mt-3">
        <i class="fas fa-plus-circle mr-1"></i> {{ action_text }}
    </a>
    {% endif %}
</div>
{% endmacro %}
