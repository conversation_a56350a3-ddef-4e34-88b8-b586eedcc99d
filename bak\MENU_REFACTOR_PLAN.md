# MenuPlan + MenuRecipe 模块删除实施计划

## 重构目标
删除 MenuPlan 和 MenuRecipe 表及相关代码，统一使用 WeeklyMenu + WeeklyMenuRecipe 作为唯一菜单数据源，确保学校级数据隔离。

## 当前依赖分析

### 1. 数据库表依赖
- **ConsumptionPlan**: 有 menu_plan_id 外键，但已设置为 nullable=True
- **FoodSample**: 有 menu_plan_id 外键，但已设置为 nullable=True
- **MenuRecipe**: 依赖 MenuPlan 的子表

### 2. 代码模块依赖
- **app/routes/menu_plan.py**: 主要路由文件（已大部分重定向）
- **app/models.py**: MenuPlan 和 MenuRecipe 模型定义
- **app/routes/consumption_plan.py**: 部分功能依赖 MenuPlan
- **app/routes/food_trace.py**: 溯源功能中的回退逻辑
- **app/services/daily_management_service.py**: 菜单数据获取的回退逻辑

## 实施步骤

### 第一阶段：数据安全备份
1. 备份现有 MenuPlan 和 MenuRecipe 数据
2. 分析历史数据的迁移需求
3. 确保学校级数据隔离完整性

### 第二阶段：移除外键约束
1. 移除 ConsumptionPlan.menu_plan_id 外键约束
2. 移除 FoodSample.menu_plan_id 外键约束
3. 保留字段用于历史数据查询

### 第三阶段：代码重构
1. 更新 ConsumptionPlan 创建逻辑，完全基于 WeeklyMenu
2. 更新 FoodSample 关联逻辑，基于日期和餐次
3. 移除所有对 MenuPlan/MenuRecipe 的引用
4. 更新权限和菜单配置

### 第四阶段：清理和验证
1. 删除 MenuPlan 和 MenuRecipe 表
2. 删除相关路由文件
3. 验证所有功能正常工作
4. 确保学校级数据隔离完整

## 风险控制措施

### 1. 数据安全
- 完整备份所有相关数据
- 分步骤执行，每步验证
- 保留回滚方案

### 2. 功能完整性
- 逐个模块验证功能
- 确保学校级数据隔离
- 测试所有相关业务流程

### 3. 性能影响
- 监控数据库查询性能
- 优化 WeeklyMenu 相关查询
- 确保索引配置正确

## 学校级数据隔离验证清单

### 1. WeeklyMenu 数据隔离
- [x] 所有查询都包含 area_id 过滤
- [x] 用户权限验证正确
- [x] 数据创建时正确设置 area_id

### 2. ConsumptionPlan 数据隔离
- [ ] 移除 menu_plan_id 依赖后仍保持 area_id 隔离
- [ ] 基于 WeeklyMenu 的查询包含正确的 area_id 过滤
- [ ] 用户权限验证完整

### 3. FoodSample 数据隔离
- [ ] 基于日期和餐次的关联保持 area_id 隔离
- [ ] 查询逻辑包含正确的学校级过滤
- [ ] 用户权限验证完整

## 实施时间表

### 第一天：准备和备份
- 数据备份
- 依赖分析确认
- 测试环境准备

### 第二天：外键移除和代码重构
- 移除外键约束
- 更新 ConsumptionPlan 相关代码
- 更新 FoodSample 相关代码

### 第三天：清理和验证
- 删除 MenuPlan/MenuRecipe 表和代码
- 功能验证
- 性能测试

## 回滚方案
如果出现问题，可以：
1. 恢复数据库备份
2. 恢复代码到重构前状态
3. 重新添加外键约束

## 成功标准
1. 所有菜单相关功能正常工作
2. 学校级数据隔离完整
3. 性能无明显下降
4. 代码结构更清晰简洁
