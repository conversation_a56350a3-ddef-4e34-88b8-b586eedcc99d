#!/usr/bin/env python3
"""
测试采购订单预填充功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from app.models import *
from datetime import datetime

def test_purchase_order_prefill():
    """测试采购订单预填充数据结构"""
    app = create_app()
    
    with app.app_context():
        try:
            # 模拟从消耗计划传递的数据结构
            sample_missing_ingredients = [
                {
                    'name': '土豆',
                    'shortage_quantity': 5.0,
                    'unit': '公斤',
                    'details': [
                        {
                            'meal_type': '午餐',
                            'recipe_name': '土豆丝'
                        },
                        {
                            'meal_type': '晚餐', 
                            'recipe_name': '土豆炖牛肉'
                        }
                    ]
                },
                {
                    'name': '胡萝卜',
                    'shortage_quantity': 3.0,
                    'unit': '公斤',
                    'details': [
                        {
                            'meal_type': '午餐',
                            'recipe_name': '胡萝卜炒肉'
                        }
                    ]
                },
                {
                    'name': '白菜',
                    'shortage_quantity': 2.5,
                    'unit': '公斤',
                    'details': [
                        {
                            'meal_type': '晚餐',
                            'recipe_name': '白菜豆腐汤'
                        }
                    ]
                }
            ]
            
            sample_purchase_data = {
                'area_id': 1,
                'consumption_date': '2025-06-14',
                'meal_types': ['午餐', '晚餐'],
                'missing_ingredients': sample_missing_ingredients,
                'source': 'consumption_plan_super_editor'
            }
            
            print("模拟的采购订单预填充数据结构:")
            print("=" * 50)
            print(f"区域ID: {sample_purchase_data['area_id']}")
            print(f"消耗日期: {sample_purchase_data['consumption_date']}")
            print(f"餐次: {', '.join(sample_purchase_data['meal_types'])}")
            print(f"缺少食材数量: {len(sample_purchase_data['missing_ingredients'])}")
            print()
            
            print("缺少食材详情:")
            for i, ingredient in enumerate(sample_purchase_data['missing_ingredients'], 1):
                print(f"{i}. {ingredient['name']}")
                print(f"   数量: {ingredient['shortage_quantity']} {ingredient['unit']}")
                print(f"   用于: {', '.join([f\"{d['meal_type']}({d['recipe_name']})\" for d in ingredient['details']])}")
                print()
            
            # 验证数据结构
            print("数据结构验证:")
            print("=" * 30)
            
            # 检查必需字段
            required_fields = ['area_id', 'consumption_date', 'meal_types', 'missing_ingredients']
            for field in required_fields:
                if field in sample_purchase_data:
                    print(f"✅ {field}: 存在")
                else:
                    print(f"❌ {field}: 缺失")
            
            # 检查食材数据结构
            print("\n食材数据结构验证:")
            for ingredient in sample_purchase_data['missing_ingredients']:
                ingredient_fields = ['name', 'shortage_quantity', 'unit', 'details']
                print(f"\n食材: {ingredient['name']}")
                for field in ingredient_fields:
                    if field in ingredient:
                        print(f"  ✅ {field}: {ingredient[field]}")
                    else:
                        print(f"  ❌ {field}: 缺失")
            
            print("\n" + "=" * 50)
            print("测试完成！数据结构符合预期。")
            
        except Exception as e:
            print(f"测试出错: {str(e)}")
            import traceback
            traceback.print_exc()

if __name__ == '__main__':
    test_purchase_order_prefill()
