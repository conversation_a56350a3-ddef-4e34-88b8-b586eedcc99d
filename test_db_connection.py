#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接测试脚本
用于验证新的数据库配置是否正常工作
"""

import os
import sys
import pyodbc
from urllib.parse import quote_plus

def test_direct_connection():
    """直接测试数据库连接"""
    print("=== 直接连接测试 ===")
    try:
        # 使用新的数据库配置
        conn_str = "DRIVER={SQL Server};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;UID=StudentsCMSSP;PWD=**************"
        
        print(f"连接字符串: {conn_str}")
        
        # 尝试连接
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()[0]
        print(f"✓ 数据库连接成功!")
        print(f"✓ 数据库版本: {version}")
        
        # 测试数据库名称
        cursor.execute("SELECT DB_NAME()")
        db_name = cursor.fetchone()[0]
        print(f"✓ 当前数据库: {db_name}")
        
        # 测试表是否存在
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
        """)
        table_count = cursor.fetchone()[0]
        print(f"✓ 数据库表数量: {table_count}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 直接连接失败: {str(e)}")
        return False

def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    print("\n=== SQLAlchemy连接测试 ===")
    try:
        from sqlalchemy import create_engine, text
        from urllib.parse import quote_plus
        
        # 构建SQLAlchemy连接字符串
        conn_str = "DRIVER={SQL Server};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;UID=StudentsCMSSP;PWD=**************"
        quoted_conn_str = quote_plus(conn_str)
        sqlalchemy_uri = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"
        
        print(f"SQLAlchemy URI: {sqlalchemy_uri}")
        
        # 创建引擎
        engine = create_engine(sqlalchemy_uri)
        
        # 测试连接
        with engine.connect() as connection:
            result = connection.execute(text("SELECT @@VERSION"))
            version = result.fetchone()[0]
            print(f"✓ SQLAlchemy连接成功!")
            print(f"✓ 数据库版本: {version}")
            
            # 测试数据库名称
            result = connection.execute(text("SELECT DB_NAME()"))
            db_name = result.fetchone()[0]
            print(f"✓ 当前数据库: {db_name}")
        
        return True
        
    except Exception as e:
        print(f"✗ SQLAlchemy连接失败: {str(e)}")
        return False

def test_flask_app_connection():
    """测试Flask应用连接"""
    print("\n=== Flask应用连接测试 ===")
    try:
        # 添加项目根目录到Python路径
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from config import Config
        
        # 创建应用
        app = create_app(Config)
        
        with app.app_context():
            # 测试数据库连接
            result = db.session.execute(db.text("SELECT @@VERSION"))
            version = result.fetchone()[0]
            print(f"✓ Flask应用数据库连接成功!")
            print(f"✓ 数据库版本: {version}")
            
            # 测试数据库名称
            result = db.session.execute(db.text("SELECT DB_NAME()"))
            db_name = result.fetchone()[0]
            print(f"✓ 当前数据库: {db_name}")
            
            # 检查配置
            print(f"✓ 数据库URI: {app.config['SQLALCHEMY_DATABASE_URI']}")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask应用连接失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("数据库连接测试开始...")
    print("=" * 50)
    
    # 测试结果
    results = []
    
    # 1. 直接连接测试
    results.append(("直接连接", test_direct_connection()))
    
    # 2. SQLAlchemy连接测试
    results.append(("SQLAlchemy连接", test_sqlalchemy_connection()))
    
    # 3. Flask应用连接测试
    results.append(("Flask应用连接", test_flask_app_connection()))
    
    # 输出测试结果
    print("\n" + "=" * 50)
    print("测试结果汇总:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过! 数据库配置正确!")
    else:
        print("❌ 部分测试失败，请检查数据库配置!")
    
    return all_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
