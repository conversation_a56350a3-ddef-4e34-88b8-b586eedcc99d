#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SQL Server身份验证测试脚本
"""

import pyodbc

def test_common_credentials():
    """测试常见的SQL Server凭据"""
    print("=== 测试常见SQL Server凭据 ===")
    
    server = "14.103.246.164"
    database = "StudentsCMSSP"
    
    # 常见的SQL Server凭据组合
    credentials = [
        # 原始提供的凭据
        ("StudentsCMSSP", "Xg2LS44Cyz5Zt8"),
        
        # 常见的管理员账户
        ("sa", "Xg2LS44Cyz5Zt8"),
        ("sa", "StudentsCMSSP"),
        ("sa", "123456"),
        ("sa", "admin"),
        ("sa", "password"),
        
        # 可能的用户变体
        ("studentscmssp", "Xg2LS44Cyz5Zt8"),
        ("STUDENTSCMSSP", "Xg2LS44Cyz5Zt8"),
        ("StudentsCMSSP", "studentscmssp"),
        ("StudentsCMSSP", "STUDENTSCMSSP"),
        
        # 其他可能的管理员账户
        ("admin", "Xg2LS44Cyz5Zt8"),
        ("administrator", "Xg2LS44Cyz5Zt8"),
        ("sqlserver", "Xg2LS44Cyz5Zt8"),
        ("cms", "Xg2LS44Cyz5Zt8"),
        
        # 空密码尝试
        ("StudentsCMSSP", ""),
        ("sa", ""),
    ]
    
    for username, password in credentials:
        print(f"\n尝试用户: {username} / 密码: {'*' * len(password) if password else '(空)'}")
        
        try:
            conn_str = f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
            
            conn = pyodbc.connect(conn_str, timeout=10)
            cursor = conn.cursor()
            
            # 获取用户信息
            cursor.execute("SELECT SYSTEM_USER, USER_NAME(), @@SERVERNAME")
            user_info = cursor.fetchone()
            
            print(f"✓ 连接成功!")
            print(f"  系统用户: {user_info[0]}")
            print(f"  数据库用户: {user_info[1]}")
            print(f"  服务器名: {user_info[2]}")
            
            cursor.close()
            conn.close()
            
            return (username, password)
            
        except Exception as e:
            error_msg = str(e)
            if "18456" in error_msg:
                print(f"✗ 登录失败 (用户名或密码错误)")
            elif "18452" in error_msg:
                print(f"✗ 登录失败 (用户不存在)")
            elif "18470" in error_msg:
                print(f"✗ 登录失败 (用户被禁用)")
            else:
                print(f"✗ 连接失败: {error_msg}")
    
    return None

def test_windows_authentication():
    """测试Windows身份验证"""
    print("\n=== 测试Windows身份验证 ===")
    
    server = "14.103.246.164"
    database = "StudentsCMSSP"
    
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};Trusted_Connection=yes"
        print(f"连接字符串: {conn_str}")
        
        conn = pyodbc.connect(conn_str, timeout=10)
        cursor = conn.cursor()
        
        cursor.execute("SELECT SYSTEM_USER, USER_NAME(), @@SERVERNAME")
        user_info = cursor.fetchone()
        
        print(f"✓ Windows身份验证成功!")
        print(f"  系统用户: {user_info[0]}")
        print(f"  数据库用户: {user_info[1]}")
        print(f"  服务器名: {user_info[2]}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ Windows身份验证失败: {str(e)}")
        return False

def test_master_database_access():
    """测试master数据库访问"""
    print("\n=== 测试master数据库访问 ===")
    
    server = "14.103.246.164"
    
    credentials = [
        ("StudentsCMSSP", "Xg2LS44Cyz5Zt8"),
        ("sa", "Xg2LS44Cyz5Zt8"),
        ("sa", "StudentsCMSSP"),
    ]
    
    for username, password in credentials:
        print(f"\n尝试连接master数据库 - 用户: {username}")
        
        try:
            conn_str = f"DRIVER={{SQL Server}};SERVER={server};DATABASE=master;UID={username};PWD={password}"
            
            conn = pyodbc.connect(conn_str, timeout=10)
            cursor = conn.cursor()
            
            # 检查可访问的数据库
            cursor.execute("""
                SELECT name FROM sys.databases 
                WHERE HAS_DBACCESS(name) = 1
                ORDER BY name
            """)
            databases = cursor.fetchall()
            
            print(f"✓ master数据库连接成功!")
            print(f"  可访问的数据库:")
            for db in databases:
                print(f"    - {db[0]}")
            
            # 检查StudentsCMSSP数据库是否存在
            cursor.execute("SELECT name FROM sys.databases WHERE name = 'StudentsCMSSP'")
            target_db = cursor.fetchone()
            
            if target_db:
                print(f"  ✓ StudentsCMSSP数据库存在")
            else:
                print(f"  ✗ StudentsCMSSP数据库不存在")
            
            cursor.close()
            conn.close()
            
            return (username, password)
            
        except Exception as e:
            print(f"✗ master数据库连接失败: {str(e)}")
    
    return None

def test_different_database_names():
    """测试不同的数据库名称"""
    print("\n=== 测试不同的数据库名称 ===")
    
    server = "14.103.246.164"
    username = "StudentsCMSSP"
    password = "Xg2LS44Cyz5Zt8"
    
    # 可能的数据库名称
    database_names = [
        "StudentsCMSSP",
        "studentscmssp",
        "STUDENTSCMSSP",
        "Students_CMSSP",
        "StudentsDB",
        "CMS",
        "CMSSP",
        "master",
        "tempdb"
    ]
    
    for db_name in database_names:
        print(f"\n尝试数据库: {db_name}")
        
        try:
            conn_str = f"DRIVER={{SQL Server}};SERVER={server};DATABASE={db_name};UID={username};PWD={password}"
            
            conn = pyodbc.connect(conn_str, timeout=10)
            cursor = conn.cursor()
            
            cursor.execute("SELECT DB_NAME()")
            current_db = cursor.fetchone()[0]
            
            print(f"✓ 连接成功! 当前数据库: {current_db}")
            
            cursor.close()
            conn.close()
            
            return db_name
            
        except Exception as e:
            print(f"✗ 连接失败: {str(e)}")
    
    return None

def main():
    """主函数"""
    print("SQL Server身份验证测试开始...")
    print("=" * 60)
    
    # 测试结果
    working_creds = None
    working_db = None
    windows_auth = False
    
    # 1. 测试常见凭据
    working_creds = test_common_credentials()
    
    # 2. 测试Windows身份验证
    windows_auth = test_windows_authentication()
    
    # 3. 测试master数据库
    master_creds = test_master_database_access()
    
    # 4. 测试不同数据库名称
    if not working_creds and master_creds:
        working_db = test_different_database_names()
    
    # 输出结果和建议
    print("\n" + "=" * 60)
    print("测试结果和建议:")
    print("=" * 60)
    
    if working_creds:
        username, password = working_creds
        print(f"✓ 找到可用的SQL Server凭据:")
        print(f"  用户名: {username}")
        print(f"  密码: {password}")
        print(f"  数据库: StudentsCMSSP")
        
        # 生成新的配置
        conn_str = f"DRIVER={{SQL Server}};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;UID={username};PWD={password}"
        print(f"\n建议的连接字符串:")
        print(f"  {conn_str}")
        
    elif windows_auth:
        print(f"✓ Windows身份验证可用")
        print(f"建议使用Windows身份验证连接")
        
    elif master_creds and working_db:
        username, password = master_creds
        print(f"✓ 找到可用的凭据和数据库:")
        print(f"  用户名: {username}")
        print(f"  密码: {password}")
        print(f"  数据库: {working_db}")
        
    else:
        print("❌ 所有身份验证尝试都失败了")
        print("\n建议联系数据库管理员:")
        print("1. 确认用户名和密码是否正确")
        print("2. 确认SQL Server启用了SQL Server身份验证")
        print("3. 确认用户有访问目标数据库的权限")
        print("4. 检查用户是否被禁用或锁定")

if __name__ == "__main__":
    main()
