#!/usr/bin/env python3
"""
周菜单功能测试脚本
测试 MenuPlan/MenuRecipe 删除后的周菜单功能是否正常
"""

import requests
import json
from datetime import datetime, date, timedelta

class WeeklyMenuTester:
    def __init__(self, base_url='http://localhost:8080'):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
    
    def log_test(self, test_name, success, message=""):
        """记录测试结果"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            'test': test_name,
            'status': status,
            'message': message
        })
        print(f"{status} {test_name}: {message}")
    
    def test_application_startup(self):
        """测试应用是否正常启动"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                self.log_test("应用启动", True, "应用正常响应")
                return True
            else:
                self.log_test("应用启动", False, f"HTTP状态码: {response.status_code}")
                return False
        except Exception as e:
            self.log_test("应用启动", False, f"连接失败: {str(e)}")
            return False
    
    def test_weekly_menu_routes(self):
        """测试周菜单路由是否可访问"""
        routes_to_test = [
            ('/weekly-menu', '周菜单列表页'),
            ('/weekly-menu/plan', '周菜单计划页'),
        ]
        
        for route, description in routes_to_test:
            try:
                response = self.session.get(f"{self.base_url}{route}")
                # 302重定向到登录页面是正常的（需要登录）
                if response.status_code in [200, 302]:
                    self.log_test(f"路由测试 - {description}", True, f"状态码: {response.status_code}")
                else:
                    self.log_test(f"路由测试 - {description}", False, f"状态码: {response.status_code}")
            except Exception as e:
                self.log_test(f"路由测试 - {description}", False, f"错误: {str(e)}")
    
    def test_menu_plan_routes_removed(self):
        """测试 menu_plan 路由是否已被移除"""
        old_routes = [
            '/menu-plan',
            '/menu-plan/create',
            '/menu-plan/week',
        ]
        
        for route in old_routes:
            try:
                response = self.session.get(f"{self.base_url}{route}")
                # 404表示路由已被移除，这是我们期望的结果
                if response.status_code == 404:
                    self.log_test(f"旧路由移除 - {route}", True, "路由已正确移除")
                else:
                    self.log_test(f"旧路由移除 - {route}", False, f"路由仍存在，状态码: {response.status_code}")
            except Exception as e:
                self.log_test(f"旧路由移除 - {route}", False, f"测试错误: {str(e)}")
    
    def test_database_tables(self):
        """测试数据库表状态（需要数据库连接）"""
        # 这个测试需要直接数据库访问，在这里只做占位
        self.log_test("数据库表检查", True, "需要手动执行 SQL 检查")
    
    def test_template_files(self):
        """测试模板文件是否已清理"""
        import os
        from pathlib import Path
        
        # 检查 menu_plan 模板目录是否已删除
        menu_plan_template_dir = Path('app/templates/menu_plan')
        if not menu_plan_template_dir.exists():
            self.log_test("模板清理 - menu_plan目录", True, "目录已删除")
        else:
            self.log_test("模板清理 - menu_plan目录", False, "目录仍然存在")
        
        # 检查清理脚本是否存在
        cleanup_script = Path('cleanup_menu_plan_templates.py')
        if cleanup_script.exists():
            self.log_test("清理脚本", True, "清理脚本已创建")
        else:
            self.log_test("清理脚本", False, "清理脚本不存在")
    
    def test_import_errors(self):
        """测试是否有导入错误"""
        try:
            # 尝试导入主要模块
            import sys
            import os
            sys.path.insert(0, os.path.abspath('.'))
            
            # 测试主要模块导入
            from app import create_app
            app = create_app()
            
            self.log_test("模块导入", True, "所有模块导入成功")
            return True
        except ImportError as e:
            self.log_test("模块导入", False, f"导入错误: {str(e)}")
            return False
        except Exception as e:
            self.log_test("模块导入", False, f"其他错误: {str(e)}")
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🧪 开始周菜单功能测试...")
        print("=" * 50)
        
        # 测试应用启动
        if not self.test_application_startup():
            print("⚠️  应用未启动，跳过需要HTTP访问的测试")
        else:
            # 测试路由
            self.test_weekly_menu_routes()
            self.test_menu_plan_routes_removed()
        
        # 测试其他功能
        self.test_template_files()
        self.test_import_errors()
        self.test_database_tables()
        
        # 输出测试总结
        print("\n" + "=" * 50)
        print("📊 测试结果总结:")
        
        passed = sum(1 for result in self.test_results if "✅" in result['status'])
        failed = sum(1 for result in self.test_results if "❌" in result['status'])
        
        print(f"总测试数: {len(self.test_results)}")
        print(f"通过: {passed}")
        print(f"失败: {failed}")
        
        if failed > 0:
            print("\n❌ 失败的测试:")
            for result in self.test_results:
                if "❌" in result['status']:
                    print(f"  - {result['test']}: {result['message']}")
        
        print("\n📋 手动检查项目:")
        print("1. 执行 app/sql/cleanup_menu_tables.sql 清理数据库表")
        print("2. 测试周菜单创建、编辑、发布功能")
        print("3. 测试消耗计划从周菜单创建功能")
        print("4. 检查导航菜单中的链接是否正确")
        print("5. 验证学校级数据隔离是否正常工作")

if __name__ == '__main__':
    tester = WeeklyMenuTester()
    tester.run_all_tests()
