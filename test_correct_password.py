#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试正确的密码连接
"""

import pyodbc
from urllib.parse import quote_plus

def test_direct_connection():
    """测试直接连接"""
    print("=== 测试直接连接（正确密码）===")
    
    try:
        # 使用正确的密码（包含点号）
        conn_str = "DRIVER={SQL Server};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;UID=StudentsCMSSP;PWD=***************"
        print(f"连接字符串: {conn_str}")
        
        conn = pyodbc.connect(conn_str, timeout=10)
        cursor = conn.cursor()
        
        # 测试查询
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()[0]
        
        cursor.execute("SELECT DB_NAME()")
        db_name = cursor.fetchone()[0]
        
        cursor.execute("SELECT SYSTEM_USER, USER_NAME()")
        user_info = cursor.fetchone()
        
        print(f"✓ 连接成功!")
        print(f"  数据库: {db_name}")
        print(f"  系统用户: {user_info[0]}")
        print(f"  数据库用户: {user_info[1]}")
        print(f"  版本: {version[:80]}...")
        
        # 测试表数量
        cursor.execute("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'")
        table_count = cursor.fetchone()[0]
        print(f"  表数量: {table_count}")
        
        cursor.close()
        conn.close()
        
        return True
        
    except Exception as e:
        print(f"✗ 连接失败: {str(e)}")
        return False

def test_sqlalchemy_connection():
    """测试SQLAlchemy连接"""
    print("\n=== 测试SQLAlchemy连接（正确密码）===")
    
    try:
        from sqlalchemy import create_engine, text
        
        # 构建SQLAlchemy连接字符串
        conn_str = "DRIVER={SQL Server};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;UID=StudentsCMSSP;PWD=***************"
        quoted_conn_str = quote_plus(conn_str)
        sqlalchemy_uri = f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"
        
        print(f"SQLAlchemy URI: {sqlalchemy_uri}")
        
        engine = create_engine(sqlalchemy_uri)
        
        with engine.connect() as connection:
            result = connection.execute(text("SELECT @@VERSION"))
            version = result.fetchone()[0]
            
            result = connection.execute(text("SELECT DB_NAME()"))
            db_name = result.fetchone()[0]
            
            result = connection.execute(text("SELECT SYSTEM_USER, USER_NAME()"))
            user_info = result.fetchone()
            
            print(f"✓ SQLAlchemy连接成功!")
            print(f"  数据库: {db_name}")
            print(f"  系统用户: {user_info[0]}")
            print(f"  数据库用户: {user_info[1]}")
            print(f"  版本: {version[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ SQLAlchemy连接失败: {str(e)}")
        return False

def test_flask_app():
    """测试Flask应用连接"""
    print("\n=== 测试Flask应用连接 ===")
    
    try:
        import sys
        import os
        sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
        
        from app import create_app, db
        from config import Config
        
        app = create_app(Config)
        
        with app.app_context():
            result = db.session.execute(db.text("SELECT @@VERSION"))
            version = result.fetchone()[0]
            
            result = db.session.execute(db.text("SELECT DB_NAME()"))
            db_name = result.fetchone()[0]
            
            print(f"✓ Flask应用连接成功!")
            print(f"  数据库: {db_name}")
            print(f"  版本: {version[:80]}...")
            print(f"  配置URI: {app.config['SQLALCHEMY_DATABASE_URI'][:100]}...")
        
        return True
        
    except Exception as e:
        print(f"✗ Flask应用连接失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("数据库连接测试（正确密码）")
    print("=" * 50)
    
    results = []
    
    # 1. 直接连接测试
    results.append(("直接连接", test_direct_connection()))
    
    # 2. SQLAlchemy连接测试
    results.append(("SQLAlchemy连接", test_sqlalchemy_connection()))
    
    # 3. Flask应用连接测试
    results.append(("Flask应用连接", test_flask_app()))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果:")
    print("=" * 50)
    
    all_passed = True
    for test_name, passed in results:
        status = "✓ 通过" if passed else "✗ 失败"
        print(f"{test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("=" * 50)
    if all_passed:
        print("🎉 所有测试通过! 数据库配置已修复!")
        print("\n数据库连接信息:")
        print("  服务器: 14.103.246.164")
        print("  数据库: StudentsCMSSP")
        print("  用户名: StudentsCMSSP")
        print("  密码: ***************")
    else:
        print("❌ 部分测试失败，需要进一步检查")
    
    return all_passed

if __name__ == "__main__":
    main()
