<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>财务凭证编辑页面演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <style>
        /* 凭证编辑页面样式 */
        .voucher-edit-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
        }

        /* 凭证表头区域 */
        .voucher-header-section {
            background: #fff;
            border: 2px solid #000;
            padding: 15px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .voucher-header-title {
            text-align: center;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .voucher-header-form .form-control-sm {
            display: inline-block;
            margin: 0 5px;
            border: 1px solid #000;
        }

        .voucher-header-form .form-label {
            font-weight: bold;
            margin-right: 5px;
        }

        /* 凭证表格区域 */
        .voucher-table-section {
            background: #fff;
            border: 2px solid #000;
            padding: 0;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .voucher-table {
            width: 100%;
            border-collapse: collapse;
            margin: 0;
            font-size: 14px;
        }

        .voucher-table th,
        .voucher-table td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }

        .voucher-table th {
            background-color: #f8f9fa;
            font-weight: bold;
            color: #333;
            position: relative;
        }

        /* 金额单位标识样式 */
        .amount-units {
            display: flex;
            justify-content: space-between;
            margin-top: 5px;
            padding: 0 5px;
            font-size: 11px;
            font-weight: normal;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 3px;
        }

        .amount-units span {
            flex: 1;
            text-align: center;
            padding: 1px 2px;
            background-color: rgba(255, 255, 255, 0.8);
            border-right: 1px solid #eee;
        }

        .amount-units span:last-child {
            border-right: none;
        }

        .voucher-table .form-control {
            border: none;
            background: transparent;
            text-align: center;
            padding: 4px;
            font-size: 14px;
            width: 100%;
            box-shadow: none;
        }

        .voucher-table .form-control:focus {
            outline: none;
            box-shadow: inset 0 0 3px #007bff;
        }

        /* 金额输入框的分位线效果 */
        .voucher-table .amount-input {
            text-align: right;
            background-image:
                /* 千万位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(10% - 0.5px),
                    #b0b0b0 calc(10% - 0.5px),
                    #b0b0b0 calc(10% + 0.5px),
                    transparent calc(10% + 0.5px)
                ),
                /* 百万位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(20% - 0.5px),
                    #b0b0b0 calc(20% - 0.5px),
                    #b0b0b0 calc(20% + 0.5px),
                    transparent calc(20% + 0.5px)
                ),
                /* 十万位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(30% - 0.5px),
                    #b0b0b0 calc(30% - 0.5px),
                    #b0b0b0 calc(30% + 0.5px),
                    transparent calc(30% + 0.5px)
                ),
                /* 万位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(40% - 0.5px),
                    #c0c0c0 calc(40% - 0.5px),
                    #c0c0c0 calc(40% + 0.5px),
                    transparent calc(40% + 0.5px)
                ),
                /* 千位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(50% - 0.5px),
                    #b0b0b0 calc(50% - 0.5px),
                    #b0b0b0 calc(50% + 0.5px),
                    transparent calc(50% + 0.5px)
                ),
                /* 百位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(60% - 0.5px),
                    #b0b0b0 calc(60% - 0.5px),
                    #b0b0b0 calc(60% + 0.5px),
                    transparent calc(60% + 0.5px)
                ),
                /* 十位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(70% - 0.5px),
                    #b0b0b0 calc(70% - 0.5px),
                    #b0b0b0 calc(70% + 0.5px),
                    transparent calc(70% + 0.5px)
                ),
                /* 元位分隔线（加粗） */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(80% - 1px),
                    #808080 calc(80% - 1px),
                    #808080 calc(80% + 1px),
                    transparent calc(80% + 1px)
                ),
                /* 角位分隔线 */
                linear-gradient(
                    to right,
                    transparent 0px,
                    transparent calc(90% - 0.5px),
                    #b0b0b0 calc(90% - 0.5px),
                    #b0b0b0 calc(90% + 0.5px),
                    transparent calc(90% + 0.5px)
                );
            background-color: #fafafa;
            font-family: 'Consolas', 'Courier New', 'SimSun', monospace;
            font-weight: bold;
            font-size: 14px;
            padding: 8px 3px;
            letter-spacing: 0px;
            min-height: 40px;
            border-radius: 0;
            color: #333;
            width: 100%;
        }

        /* 金额输入框聚焦时的效果 */
        .voucher-table .amount-input:focus {
            background-color: rgba(255, 255, 240, 0.8);
            box-shadow: inset 0 0 8px rgba(0, 123, 255, 0.2);
            outline: none;
        }

        /* 金额输入框有值时的样式 */
        .voucher-table .amount-input:not(:placeholder-shown) {
            background-color: #fff;
            color: #000;
            font-weight: 600;
        }

        /* 占位符样式 */
        .voucher-table .amount-input::placeholder {
            color: transparent;
            font-size: 12px;
        }

        /* 数字对齐优化 */
        .voucher-table .amount-input {
            direction: ltr;
            unicode-bidi: bidi-override;
        }

        /* 合计行样式 */
        .total-row {
            background-color: #f8f9fa;
            font-weight: bold;
            border-top: 2px solid #000;
        }

        .total-row td {
            font-size: 16px;
            color: #333;
        }

        /* 金额大写行样式 */
        .amount-words-row {
            background-color: #fff;
            border-top: 1px solid #000;
        }

        .amount-words-row td {
            text-align: left;
            padding: 12px;
            font-weight: bold;
            font-size: 14px;
            color: #333;
        }

        /* 凭证操作按钮区域 */
        .voucher-actions {
            padding: 15px;
            text-align: center;
            border-top: 1px solid #000;
            background-color: #f8f9fa;
        }

        .voucher-actions .btn {
            margin: 0 10px;
            min-width: 120px;
        }

        /* 签字区域 */
        .voucher-signature-section {
            background: #fff;
            border: 2px solid #000;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .signature-row {
            display: flex;
            justify-content: space-around;
            align-items: center;
        }

        .signature-item {
            display: flex;
            align-items: center;
            flex-direction: column;
        }

        .signature-label {
            margin-bottom: 5px;
            font-weight: bold;
            font-size: 14px;
        }

        .signature-box {
            display: inline-block;
            width: 80px;
            height: 30px;
            border-bottom: 2px solid #000;
            text-align: center;
            line-height: 30px;
            font-size: 12px;
        }

        /* 按钮样式增强 */
        .btn-sm {
            padding: 0.2rem 0.4rem;
            font-size: 0.75rem;
            border-radius: 0.2rem;
            min-width: 32px;
        }

        /* 操作列按钮组 */
        .voucher-table .btn-group-sm {
            display: flex;
            gap: 2px;
        }

        .voucher-table .btn-group-sm .btn {
            flex: 1;
            padding: 0.15rem 0.3rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .voucher-edit-container {
                padding: 10px;
            }
            
            .voucher-header-form .row {
                flex-direction: column;
            }
            
            .voucher-header-form .col-auto {
                margin-bottom: 10px;
                width: 100%;
            }
            
            .voucher-header-form .form-control-sm {
                width: 100%;
                margin: 5px 0;
            }
            
            .signature-row {
                flex-direction: column;
                gap: 15px;
            }
            
            .signature-item {
                width: 100%;
                flex-direction: row;
                justify-content: space-between;
            }
            
            .voucher-table {
                font-size: 12px;
            }
            
            .voucher-table th,
            .voucher-table td {
                padding: 4px;
            }
            
            .voucher-actions .btn {
                margin: 5px;
                min-width: 100px;
            }
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="voucher-edit-container">
            <!-- 凭证表头 -->
            <div class="voucher-header-section">
                <div class="voucher-header-title">
                    <h4 class="mb-0">记账凭证</h4>
                    <span class="badge badge-warning ml-2">草稿</span>
                </div>
                
                <div class="voucher-header-form">
                    <div class="row align-items-center">
                        <div class="col-auto">
                            <label class="form-label mb-0">字：</label>
                            <select class="form-control form-control-sm d-inline-block" id="voucherType" style="width: 80px;">
                                <option value="记" selected>记</option>
                                <option value="收">收</option>
                                <option value="付">付</option>
                            </select>
                        </div>
                        <div class="col-auto">
                            <label class="form-label mb-0">号：</label>
                            <input type="text" class="form-control form-control-sm d-inline-block" 
                                   value="001" readonly style="width: 80px;">
                        </div>
                        <div class="col-auto">
                            <label class="form-label mb-0">日期：</label>
                            <input type="date" class="form-control form-control-sm d-inline-block" 
                                   id="voucherDate" value="2024-01-15" style="width: 150px;">
                        </div>
                        <div class="col-auto">
                            <label class="form-label mb-0">期：</label>
                            <input type="text" class="form-control form-control-sm d-inline-block" 
                                   id="voucherPeriod" value="01" style="width: 60px;">
                        </div>
                        <div class="col-auto">
                            <label class="form-label mb-0">附单据：</label>
                            <input type="number" class="form-control form-control-sm d-inline-block" 
                                   id="attachmentCount" value="2" min="0" style="width: 60px;">
                            <span class="ml-1">张</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 凭证明细表格 -->
            <div class="voucher-table-section">
                <table class="voucher-table">
                    <thead>
                        <tr>
                            <th width="18%">摘要</th>
                            <th width="28%">会计科目</th>
                            <th width="20%">
                                <div>借方金额</div>
                                <div class="amount-units">
                                    <span>千</span><span>百</span><span>十</span><span>万</span><span>千</span><span>百</span><span>十</span><span>元</span><span>角</span><span>分</span>
                                </div>
                            </th>
                            <th width="20%">
                                <div>贷方金额</div>
                                <div class="amount-units">
                                    <span>千</span><span>百</span><span>十</span><span>万</span><span>千</span><span>百</span><span>十</span><span>元</span><span>角</span><span>分</span>
                                </div>
                            </th>
                            <th width="14%">操作</th>
                        </tr>
                    </thead>
                    <tbody id="voucher-rows">
                        <tr>
                            <td>
                                <input type="text" class="form-control summary-input" 
                                       value="购买办公用品" readonly>
                            </td>
                            <td>
                                <input type="text" class="form-control subject-input" 
                                       value="5001 - 办公费" readonly>
                                <input type="hidden" class="subject-id" value="5001">
                            </td>
                            <td>
                                <input type="text" class="form-control debit-input amount-input"
                                       value="0000015000" readonly placeholder="0000000000">
                            </td>
                            <td>
                                <input type="text" class="form-control credit-input amount-input"
                                       value="" readonly placeholder="0000000000">
                            </td>
                            <td>
                                <div class="btn-group-sm">
                                    <button type="button" class="btn btn-sm btn-warning edit-row-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger delete-row-btn" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <input type="text" class="form-control summary-input" 
                                       value="支付现金" readonly>
                            </td>
                            <td>
                                <input type="text" class="form-control subject-input" 
                                       value="1001 - 库存现金" readonly>
                                <input type="hidden" class="subject-id" value="1001">
                            </td>
                            <td>
                                <input type="text" class="form-control debit-input amount-input"
                                       value="" readonly placeholder="0000000000">
                            </td>
                            <td>
                                <input type="text" class="form-control credit-input amount-input"
                                       value="0000015000" readonly placeholder="0000000000">
                            </td>
                            <td>
                                <div class="btn-group-sm">
                                    <button type="button" class="btn btn-sm btn-warning edit-row-btn" title="编辑">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-danger delete-row-btn" title="删除">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr class="total-row">
                            <td colspan="2"><strong>合计：</strong></td>
                            <td><strong id="debit-total">1,500.00</strong></td>
                            <td><strong id="credit-total">1,500.00</strong></td>
                            <td></td>
                        </tr>
                        <tr class="amount-words-row">
                            <td colspan="5" id="amount-in-words">合计大写：壹仟伍佰元整</td>
                        </tr>
                    </tfoot>
                </table>
                
                <div class="voucher-actions mt-3">
                    <button type="button" class="btn btn-success" id="add-row-btn">
                        <i class="fas fa-plus"></i> 添加分录
                    </button>
                    <button type="button" class="btn btn-primary" id="save-voucher-btn">
                        <i class="fas fa-save"></i> 保存凭证
                    </button>
                </div>
            </div>

            <!-- 签字区域 -->
            <div class="voucher-signature-section">
                <div class="signature-row">
                    <div class="signature-item">
                        <span class="signature-label">审核：</span>
                        <span class="signature-box"></span>
                    </div>
                    <div class="signature-item">
                        <span class="signature-label">记账：</span>
                        <span class="signature-box"></span>
                    </div>
                    <div class="signature-item">
                        <span class="signature-label">出纳：</span>
                        <span class="signature-box"></span>
                    </div>
                    <div class="signature-item">
                        <span class="signature-label">制单：</span>
                        <span class="signature-box">张三</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 金额格式化函数
        function formatAmount(amount) {
            // 将金额转换为10位字符串（千万到分）
            const amountStr = (parseFloat(amount) * 100).toFixed(0).padStart(10, '0');
            return amountStr;
        }

        // 金额验证和格式化
        function validateAndFormatAmount(input) {
            let value = input.value.replace(/[^\d.]/g, ''); // 只保留数字和小数点
            if (value) {
                const formatted = formatAmount(value);
                input.value = formatted;
            }
        }

        // 简单的演示功能
        document.addEventListener('DOMContentLoaded', function() {
            console.log('凭证编辑页面演示加载完成');

            // 金额输入框事件
            document.querySelectorAll('.amount-input').forEach(function(input) {
                input.addEventListener('blur', function() {
                    validateAndFormatAmount(this);
                });

                // 双击编辑演示
                input.addEventListener('dblclick', function() {
                    if (this.readOnly) {
                        alert('双击编辑金额演示 - 实际项目中会启用编辑模式');
                    }
                });
            });

            // 添加分录按钮演示
            document.getElementById('add-row-btn').addEventListener('click', function() {
                alert('添加分录功能演示 - 在实际项目中会打开会计科目选择器');
            });

            // 保存凭证按钮演示
            document.getElementById('save-voucher-btn').addEventListener('click', function() {
                alert('保存凭证功能演示 - 在实际项目中会保存到数据库');
            });

            // 编辑按钮演示
            document.addEventListener('click', function(e) {
                if (e.target.closest('.edit-row-btn')) {
                    alert('编辑分录功能演示 - 在实际项目中会启用行编辑模式');
                } else if (e.target.closest('.delete-row-btn')) {
                    if (confirm('确定要删除这条分录吗？')) {
                        e.target.closest('tr').remove();
                        alert('删除成功（演示）');
                    }
                }
            });
        });
    </script>
</body>
</html>
