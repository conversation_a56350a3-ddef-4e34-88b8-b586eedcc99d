-- 修复 stock_ins 表的财务字段
-- 执行前请备份数据库
-- 使用方法：在 SQL Server Management Studio 中执行此脚本

USE [StudentsCMSSP]
GO

PRINT '开始修复 stock_ins 表的财务字段...'

-- 检查并添加 is_financial_confirmed 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'is_financial_confirmed')
BEGIN
    ALTER TABLE stock_ins ADD is_financial_confirmed BIT NOT NULL DEFAULT 0;
    PRINT '✓ 添加 is_financial_confirmed 字段'
END
ELSE
BEGIN
    PRINT '- is_financial_confirmed 字段已存在'
END

-- 检查并添加 total_cost 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'total_cost')
BEGIN
    ALTER TABLE stock_ins ADD total_cost DECIMAL(10,2) NULL;
    PRINT '✓ 添加 total_cost 字段'
END
ELSE
BEGIN
    PRINT '- total_cost 字段已存在'
END

-- 检查并添加 supplier_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'supplier_id')
BEGIN
    ALTER TABLE stock_ins ADD supplier_id INT NULL;
    PRINT '✓ 添加 supplier_id 字段'
END
ELSE
BEGIN
    PRINT '- supplier_id 字段已存在'
END

-- 检查并添加 payable_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'payable_id')
BEGIN
    ALTER TABLE stock_ins ADD payable_id INT NULL;
    PRINT '✓ 添加 payable_id 字段'
END
ELSE
BEGIN
    PRINT '- payable_id 字段已存在'
END

-- 检查并添加 voucher_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'voucher_id')
BEGIN
    ALTER TABLE stock_ins ADD voucher_id INT NULL;
    PRINT '✓ 添加 voucher_id 字段'
END
ELSE
BEGIN
    PRINT '- voucher_id 字段已存在'
END

-- 检查并添加 area_id 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'area_id')
BEGIN
    ALTER TABLE stock_ins ADD area_id INT NULL;
    PRINT '✓ 添加 area_id 字段'
END
ELSE
BEGIN
    PRINT '- area_id 字段已存在'
END

-- 检查并添加 purchase_order_id 字段（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'purchase_order_id')
BEGIN
    ALTER TABLE stock_ins ADD purchase_order_id INT NULL;
    PRINT '✓ 添加 purchase_order_id 字段'
END
ELSE
BEGIN
    PRINT '- purchase_order_id 字段已存在'
END

-- 检查并添加 financial_confirmed_at 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_at')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_at DATETIME2(1) NULL;
    PRINT '✓ 添加 financial_confirmed_at 字段'
END
ELSE
BEGIN
    PRINT '- financial_confirmed_at 字段已存在'
END

-- 检查并添加 financial_confirmed_by 字段
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('stock_ins') AND name = 'financial_confirmed_by')
BEGIN
    ALTER TABLE stock_ins ADD financial_confirmed_by INT NULL;
    PRINT '✓ 添加 financial_confirmed_by 字段'
END
ELSE
BEGIN
    PRINT '- financial_confirmed_by 字段已存在'
END

PRINT '字段添加完成，开始添加外键约束...'

-- 添加外键约束（如果不存在）
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_supplier_id')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_supplier_id 
        FOREIGN KEY (supplier_id) REFERENCES suppliers(id);
    PRINT '✓ 添加 supplier_id 外键约束'
END
ELSE
BEGIN
    PRINT '- supplier_id 外键约束已存在'
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_area_id')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id);
    PRINT '✓ 添加 area_id 外键约束'
END
ELSE
BEGIN
    PRINT '- area_id 外键约束已存在'
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_purchase_order_id')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_purchase_order_id 
        FOREIGN KEY (purchase_order_id) REFERENCES purchase_orders(id);
    PRINT '✓ 添加 purchase_order_id 外键约束'
END
ELSE
BEGIN
    PRINT '- purchase_order_id 外键约束已存在'
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_stock_ins_financial_confirmed_by')
BEGIN
    ALTER TABLE stock_ins ADD CONSTRAINT FK_stock_ins_financial_confirmed_by 
        FOREIGN KEY (financial_confirmed_by) REFERENCES users(id);
    PRINT '✓ 添加 financial_confirmed_by 外键约束'
END
ELSE
BEGIN
    PRINT '- financial_confirmed_by 外键约束已存在'
END

PRINT '外键约束添加完成，开始数据初始化...'

-- 为现有数据设置 area_id（从 warehouse 表获取）
UPDATE si 
SET area_id = w.area_id
FROM stock_ins si
INNER JOIN warehouses w ON si.warehouse_id = w.id
WHERE si.area_id IS NULL;

PRINT '✓ 更新现有数据的 area_id'

-- 为现有数据计算 total_cost（从入库明细计算）
UPDATE si
SET total_cost = ISNULL((
    SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
), 0)
WHERE si.total_cost IS NULL OR si.total_cost = 0;

PRINT '✓ 计算现有数据的 total_cost'

-- 为现有数据设置 supplier_id（从第一个入库明细获取）
UPDATE si
SET supplier_id = (
    SELECT TOP 1 sii.supplier_id
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
    AND sii.supplier_id IS NOT NULL
)
WHERE si.supplier_id IS NULL;

PRINT '✓ 设置现有数据的 supplier_id'

PRINT '数据初始化完成，开始创建索引...'

-- 创建索引以提高查询性能
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_is_financial_confirmed')
BEGIN
    CREATE INDEX IX_stock_ins_is_financial_confirmed ON stock_ins(is_financial_confirmed);
    PRINT '✓ 创建 is_financial_confirmed 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_supplier_id')
BEGIN
    CREATE INDEX IX_stock_ins_supplier_id ON stock_ins(supplier_id);
    PRINT '✓ 创建 supplier_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_area_id')
BEGIN
    CREATE INDEX IX_stock_ins_area_id ON stock_ins(area_id);
    PRINT '✓ 创建 area_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_payable_id')
BEGIN
    CREATE INDEX IX_stock_ins_payable_id ON stock_ins(payable_id);
    PRINT '✓ 创建 payable_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_voucher_id')
BEGIN
    CREATE INDEX IX_stock_ins_voucher_id ON stock_ins(voucher_id);
    PRINT '✓ 创建 voucher_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_stock_ins_purchase_order_id')
BEGIN
    CREATE INDEX IX_stock_ins_purchase_order_id ON stock_ins(purchase_order_id);
    PRINT '✓ 创建 purchase_order_id 索引'
END

PRINT '索引创建完成！'
PRINT ''
PRINT '🎉 stock_ins 表财务字段修复完成！'
PRINT '现在可以正常使用财务模块的应付账款功能了。'
GO
