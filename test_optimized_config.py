#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试优化后的配置
"""

import time
from datetime import datetime

def test_optimized_config():
    """测试优化后的配置"""
    print("=== 测试优化后的配置 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 测试应用创建
        start_time = time.time()
        from app import create_app, db
        from config import Config
        
        app = create_app(Config)
        creation_time = time.time() - start_time
        
        print(f"✓ 应用创建时间: {creation_time:.3f}秒")
        
        # 测试配置
        with app.app_context():
            print(f"✓ 调试模式: {app.config.get('DEBUG')}")
            print(f"✓ 数据库连接池大小: {app.config.get('SQLALCHEMY_ENGINE_OPTIONS', {}).get('pool_size')}")
            print(f"✓ 缓存类型: {app.config.get('CACHE_TYPE')}")
            print(f"✓ 日志级别: {app.config.get('LOG_LEVEL')}")
            print(f"✓ 模板自动重载: {app.config.get('TEMPLATES_AUTO_RELOAD')}")
            
            # 测试数据库连接
            start_time = time.time()
            result = db.session.execute(db.text("SELECT @@VERSION"))
            version = result.fetchone()[0]
            db_time = time.time() - start_time
            
            print(f"✓ 数据库连接时间: {db_time:.3f}秒")
            print(f"✓ 数据库版本: {version[:50]}...")
            
            # 测试多次查询
            start_time = time.time()
            for i in range(5):
                result = db.session.execute(db.text("SELECT 1"))
                result.fetchone()
            multi_query_time = time.time() - start_time
            
            print(f"✓ 5次查询总时间: {multi_query_time:.3f}秒")
            print(f"✓ 平均查询时间: {multi_query_time/5:.3f}秒")
        
        print("\n✅ 配置优化成功！")
        print("\n优化效果:")
        print("• 关闭了调试模式，提高运行速度")
        print("• 优化了数据库连接池，提高并发性能")
        print("• 启用了内存缓存，加快数据访问")
        print("• 减少了日志输出，降低I/O开销")
        print("• 关闭了模板自动重载，减少文件监控")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("配置优化测试开始...")
    print("=" * 50)
    
    success = test_optimized_config()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 配置优化完成！应用运行速度已提升")
        print("\n使用说明:")
        print("• 直接使用 'python run.py' 启动应用")
        print("• 如需调试，可临时修改 config.py 中的 DEBUG = True")
        print("• 访问地址: http://localhost:8080")
    else:
        print("❌ 配置优化失败，请检查错误信息")

if __name__ == "__main__":
    main()
