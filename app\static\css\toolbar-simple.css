/* 简化版顶部工具栏样式 - 去除过度效果 */

/* 顶部工具栏基础样式 */
.top-toolbar {
    background: var(--theme-primary) !important;
    color: white !important;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    padding: 0.75rem 1.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    z-index: 999;
}

.top-toolbar h5 {
    color: white !important;
    font-weight: 600;
    margin: 0;
}

/* 工具栏按钮简化样式 */
.toolbar-right .btn {
    margin-left: 0.25rem;
    border-radius: 4px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    color: white !important;
    border-color: rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    border: 1px solid rgba(255,255,255,0.2);
}

/* 简单的悬停效果 */
.toolbar-right .btn:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.4);
    color: white !important;
}

/* 移除过度的变换效果 */
.toolbar-right .btn:focus,
.toolbar-right .btn:active {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.4);
    color: white !important;
    box-shadow: none;
    outline: none;
}

/* 下拉菜单箭头隐藏 */
.toolbar-right .dropdown-toggle::after {
    display: none;
}

/* 移动端切换按钮 */
.mobile-toggle {
    color: white !important;
    border-color: rgba(255,255,255,0.3) !important;
    background: rgba(255,255,255,0.1) !important;
}

.mobile-toggle:hover {
    background: rgba(255,255,255,0.2) !important;
    border-color: rgba(255,255,255,0.4) !important;
    color: white !important;
}

/* 通知徽章简化 */
.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    font-size: 0.7rem;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 下拉菜单样式简化 */
.dropdown-menu {
    border: 1px solid rgba(0,0,0,0.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    border-radius: 4px;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

/* 主题预览圆点简化 */
.theme-preview {
    display: inline-block;
    width: 14px;
    height: 14px;
    border-radius: 50%;
    margin-right: 8px;
    border: 1px solid rgba(0,0,0,0.1);
}

.theme-preview.primary { background: #007bff; }
.theme-preview.secondary { background: #6c757d; }
.theme-preview.success { background: #28a745; }
.theme-preview.warning { background: #ffc107; }
.theme-preview.info { background: #17a2b8; }
.theme-preview.danger { background: #dc3545; }

/* 主题选项简化 */
.theme-option {
    display: flex;
    align-items: center;
    padding: 0.5rem 1rem;
    text-decoration: none;
    color: #333;
}

.theme-option:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
}

.theme-option.active {
    background-color: #e3f2fd;
    color: #1976d2;
    font-weight: 500;
}

/* 通知项目简化 */
.notification-item {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f0f0f0;
    text-decoration: none;
    color: #333;
    display: block;
}

.notification-item:hover {
    background-color: #f8f9fa;
    text-decoration: none;
    color: #333;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.notification-content {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 4px;
}

.notification-time {
    font-size: 0.75rem;
    color: #999;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .top-toolbar {
        padding: 0.5rem 1rem;
    }
    
    .toolbar-right .btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
        margin-left: 0.15rem;
    }
    
    .top-toolbar h5 {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .toolbar-right .btn {
        padding: 0.3rem 0.5rem;
        font-size: 0.75rem;
    }
    
    /* 隐藏按钮文字，只显示图标 */
    .toolbar-right .btn span {
        display: none;
    }
}
