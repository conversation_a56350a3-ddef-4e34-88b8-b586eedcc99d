# PowerShell脚本：修复IIS URL Rewrite模块 HTTP 500.52 错误
# 解决配置节锁定问题：allowedServerVariables 不能在站点级别配置
# 需要以管理员身份运行

Write-Host "=== 修复IIS URL Rewrite模块 HTTP 500.52 错误 ===" -ForegroundColor Green
Write-Host "错误: HTTP 错误 500.52 - URL Rewrite Module Error" -ForegroundColor Yellow
Write-Host "原因: allowedServerVariables 配置节在站点级别被锁定" -ForegroundColor Yellow
Write-Host "解决方案: 在IIS全局级别配置允许的服务器变量" -ForegroundColor Yellow

try {
    Import-Module WebAdministration -Force
    Write-Host "✓ WebAdministration模块加载成功" -ForegroundColor Green
} catch {
    Write-Host "✗ WebAdministration模块加载失败" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 第一步：检查URL Rewrite模块 ===" -ForegroundColor Yellow

# 检查URL Rewrite模块是否安装
$urlRewriteModule = Get-WebGlobalModule -Name "RewriteModule" -ErrorAction SilentlyContinue
if ($urlRewriteModule) {
    Write-Host "✓ URL Rewrite模块已安装" -ForegroundColor Green
} else {
    Write-Host "✗ URL Rewrite模块未安装，正在安装..." -ForegroundColor Red
    
    # 下载并安装URL Rewrite模块
    $url = "https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi"
    $output = "$env:TEMP\rewrite_amd64_en-US.msi"
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $output
        Start-Process msiexec.exe -Wait -ArgumentList "/i $output /quiet"
        Remove-Item $output -Force
        Write-Host "✓ URL Rewrite模块安装完成" -ForegroundColor Green
        
        # 重启IIS以加载模块
        iisreset /noforce
        Write-Host "✓ IIS服务已重启" -ForegroundColor Green
    } catch {
        Write-Host "✗ URL Rewrite模块安装失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n=== 第二步：在全局级别配置允许的服务器变量 ===" -ForegroundColor Yellow

# 配置允许的服务器变量（全局级别）
$serverVariables = @(
    "HTTP_X_FORWARDED_FOR",
    "HTTP_X_FORWARDED_PROTO", 
    "HTTP_X_FORWARDED_HOST",
    "HTTP_X_REAL_IP"
)

foreach ($variable in $serverVariables) {
    try {
        # 在全局级别添加允许的服务器变量
        Add-WebConfigurationProperty -PSPath "MACHINE/WEBROOT/APPHOST" -Filter "system.webServer/rewrite/allowedServerVariables" -Name "." -Value @{name=$variable}
        Write-Host "✓ 已在全局级别添加允许的服务器变量: $variable" -ForegroundColor Green
    } catch {
        # 如果变量已存在，会抛出异常，这是正常的
        if ($_.Exception.Message -match "already exists") {
            Write-Host "- 服务器变量已存在: $variable" -ForegroundColor Gray
        } else {
            Write-Host "- 配置服务器变量时出现警告: $variable - $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }
}

Write-Host "`n=== 第三步：修复web.config文件 ===" -ForegroundColor Yellow

$webConfigPath = "C:\StudentsCMSSP\web.config"
if (Test-Path $webConfigPath) {
    Write-Host "✓ web.config文件存在" -ForegroundColor Green
    
    # 备份原文件
    $backupPath = "$webConfigPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $webConfigPath $backupPath
    Write-Host "✓ 已备份原web.config文件到: $backupPath" -ForegroundColor Green
    
    # 检查web.config内容
    $webConfigContent = Get-Content $webConfigPath -Raw
    
    # 检查是否包含站点级别的allowedServerVariables配置（这会导致500.52错误）
    if ($webConfigContent -match "<allowedServerVariables>") {
        Write-Host "✗ web.config包含站点级别的allowedServerVariables配置（导致500.52错误）" -ForegroundColor Red
        Write-Host "正在移除站点级别的allowedServerVariables配置..." -ForegroundColor Yellow
        
        # 移除站点级别的allowedServerVariables配置
        $pattern = '(?s)\s*<!-- 允许设置的服务器变量 -->\s*<allowedServerVariables>.*?</allowedServerVariables>\s*'
        $newContent = $webConfigContent -replace $pattern, ''
        
        # 如果还有其他形式的allowedServerVariables配置，也移除
        $pattern2 = '(?s)\s*<allowedServerVariables>.*?</allowedServerVariables>\s*'
        $newContent = $newContent -replace $pattern2, ''
        
        # 写入修复后的内容
        Set-Content -Path $webConfigPath -Value $newContent -Encoding UTF8
        Write-Host "✓ 已移除站点级别的allowedServerVariables配置" -ForegroundColor Green
    } else {
        Write-Host "✓ web.config不包含站点级别的allowedServerVariables配置" -ForegroundColor Green
    }
    
    # 验证代理配置
    if ($webConfigContent -match "127\.0\.0\.1:8080") {
        Write-Host "✓ web.config包含正确的代理配置（端口8080）" -ForegroundColor Green
    } else {
        Write-Host "✗ web.config代理配置可能不正确" -ForegroundColor Red
    }
} else {
    Write-Host "✗ web.config文件不存在于: $webConfigPath" -ForegroundColor Red
}

Write-Host "`n=== 第四步：验证站点配置 ===" -ForegroundColor Yellow

$siteName = "xiaoyuanst.com"
$site = Get-Website -Name $siteName -ErrorAction SilentlyContinue
if ($site) {
    Write-Host "✓ 找到站点: $siteName" -ForegroundColor Green
    Write-Host "  状态: $($site.State)" -ForegroundColor White
    Write-Host "  物理路径: $($site.PhysicalPath)" -ForegroundColor White
} else {
    Write-Host "✗ 站点 $siteName 不存在，请先运行 setup_xiaoyuanst_domain.ps1" -ForegroundColor Red
}

Write-Host "`n=== 第五步：重启IIS服务 ===" -ForegroundColor Yellow

try {
    # 重启应用程序池
    $appPoolName = "xiaoyuanst.com_AppPool"
    if (Get-WebAppPool -Name $appPoolName -ErrorAction SilentlyContinue) {
        Restart-WebAppPool -Name $appPoolName
        Write-Host "✓ 已重启应用程序池: $appPoolName" -ForegroundColor Green
    }
    
    # 重启站点
    if ($site) {
        Stop-Website -Name $siteName
        Start-Website -Name $siteName
        Write-Host "✓ 已重启站点: $siteName" -ForegroundColor Green
    }
    
    # 重启IIS
    Write-Host "正在重启IIS服务..." -ForegroundColor Yellow
    iisreset /noforce
    Write-Host "✓ IIS服务重启完成" -ForegroundColor Green
    
} catch {
    Write-Host "✗ 重启服务时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 第六步：测试修复结果 ===" -ForegroundColor Yellow

# 等待服务启动
Start-Sleep -Seconds 5

try {
    # 测试本地访问
    Write-Host "测试本地访问..." -ForegroundColor White
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10 -ErrorAction SilentlyContinue
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ 本地访问测试成功（HTTP 200）" -ForegroundColor Green
        Write-Host "✓ HTTP 500.52 错误已修复！" -ForegroundColor Green
    } else {
        Write-Host "✗ 本地访问测试失败，状态码: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 本地访问测试失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 检查具体错误
    if ($_.Exception.Message -match "500\.52") {
        Write-Host "仍然存在500.52错误，请检查：" -ForegroundColor Yellow
        Write-Host "1. IIS管理器中的URL Rewrite模块配置" -ForegroundColor White
        Write-Host "2. applicationHost.config文件中的全局配置" -ForegroundColor White
        Write-Host "3. web.config文件是否还有站点级别的allowedServerVariables配置" -ForegroundColor White
    } elseif ($_.Exception.Message -match "500") {
        Write-Host "存在其他500错误，可能的原因：" -ForegroundColor Yellow
        Write-Host "1. Flask应用未运行或端口8080不可用" -ForegroundColor White
        Write-Host "2. web.config配置语法错误" -ForegroundColor White
        Write-Host "3. IIS权限问题" -ForegroundColor White
    }
}

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
Write-Host "修复说明：" -ForegroundColor Yellow
Write-Host "1. 已在IIS全局级别配置允许的服务器变量" -ForegroundColor White
Write-Host "2. 已从web.config中移除站点级别的allowedServerVariables配置" -ForegroundColor White
Write-Host "3. 已重启IIS服务使配置生效" -ForegroundColor White

Write-Host "`n如果问题仍然存在，请检查：" -ForegroundColor Yellow
Write-Host "1. 确保Flask应用正在运行：python run.py" -ForegroundColor White
Write-Host "2. 检查IIS日志：C:\inetpub\logs\LogFiles\" -ForegroundColor White
Write-Host "3. 检查Windows事件日志" -ForegroundColor White
Write-Host "4. 运行测试脚本：.\test_xiaoyuanst_domain.ps1" -ForegroundColor White

Write-Host "`n访问地址：" -ForegroundColor Cyan
Write-Host "- 本地访问: http://localhost" -ForegroundColor White
Write-Host "- IP访问: http://**************" -ForegroundColor White
Write-Host "- 域名访问: http://xiaoyuanst.com" -ForegroundColor White
