<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>左右式布局测试</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        /* 左右式布局样式 */
        body {
            margin: 0;
            padding: 0;
            height: 100vh;
            overflow: hidden;
        }
        
        .layout-container {
            display: flex;
            height: 100vh;
            width: 100%;
        }
        
        /* 左侧导航栏 */
        .sidebar {
            width: 200px;
            min-width: 200px;
            background: #007bff;
            color: white;
            display: flex;
            flex-direction: column;
            box-shadow: 2px 0 10px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .sidebar-header {
            padding: 1rem;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            background: linear-gradient(135deg, #007bff, #0056b3);
        }
        
        .sidebar-brand {
            color: white;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1rem;
        }
        
        .sidebar-nav {
            flex: 1;
            overflow-y: auto;
            padding: 0.5rem 0;
        }
        
        .sidebar-nav .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 0.75rem 1rem;
            border-radius: 6px;
            margin: 0.2rem 0.5rem;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 0.5rem;
            text-decoration: none;
        }
        
        .sidebar-nav .nav-link:hover {
            background: rgba(255,255,255,0.1);
            color: white;
        }
        
        /* 右侧内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            background: #f8f9fa;
        }
        
        /* 顶部工具栏 */
        .top-toolbar {
            background: white;
            border-bottom: 1px solid #dee2e6;
            padding: 0.75rem 1.5rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        /* 内容区域 */
        .content-area {
            flex: 1;
            overflow-y: auto;
            padding: 1.5rem;
            background: white;
        }
        
        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                position: fixed;
                left: -200px;
                height: 100vh;
                z-index: 1050;
                transition: left 0.3s ease;
            }
            
            .sidebar.show {
                left: 0;
            }
            
            .main-content {
                width: 100%;
            }
        }
    </style>
</head>
<body data-theme="primary">
    <div class="layout-container">
        <!-- 左侧导航栏 -->
        <div class="sidebar">
            <!-- 侧边栏头部 -->
            <div class="sidebar-header">
                <a class="sidebar-brand" href="#">
                    <i class="fas fa-utensils"></i>
                    智慧食堂平台
                </a>
            </div>
            
            <!-- 侧边栏导航 -->
            <div class="sidebar-nav">
                <a class="nav-link" href="#">
                    <i class="fas fa-home"></i>
                    首页
                </a>
                <a class="nav-link" href="#">
                    <i class="fas fa-chart-bar"></i>
                    数据统计
                </a>
                <a class="nav-link" href="#">
                    <i class="fas fa-users"></i>
                    用户管理
                </a>
                <a class="nav-link" href="#">
                    <i class="fas fa-shopping-cart"></i>
                    采购管理
                </a>
                <a class="nav-link" href="#">
                    <i class="fas fa-warehouse"></i>
                    库存管理
                </a>
                <a class="nav-link" href="#">
                    <i class="fas fa-clipboard-list"></i>
                    菜谱管理
                </a>
                <a class="nav-link" href="#">
                    <i class="fas fa-cog"></i>
                    系统设置
                </a>
            </div>
        </div>
        
        <!-- 右侧主内容区域 -->
        <div class="main-content">
            <!-- 顶部工具栏 -->
            <div class="top-toolbar">
                <div class="d-flex align-items-center">
                    <button class="btn btn-outline-secondary d-md-none me-2" onclick="toggleSidebar()">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h5 class="mb-0">左右式布局测试</h5>
                </div>
                
                <div class="d-flex align-items-center gap-2">
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-palette"></i>
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-bell"></i>
                    </button>
                    <button class="btn btn-outline-secondary">
                        <i class="fas fa-user"></i>
                        用户
                    </button>
                </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="content-area">
                <div class="row">
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">统计卡片 1</h5>
                                <p class="card-text">这是一个测试卡片，用于展示左右式布局的效果。</p>
                                <a href="#" class="btn btn-primary">查看详情</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">统计卡片 2</h5>
                                <p class="card-text">左侧导航栏固定200px宽度，右侧内容区域自适应。</p>
                                <a href="#" class="btn btn-success">查看详情</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-body">
                                <h5 class="card-title">统计卡片 3</h5>
                                <p class="card-text">支持响应式设计，移动端侧边栏可收起。</p>
                                <a href="#" class="btn btn-warning">查看详情</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">数据表格</h5>
                            </div>
                            <div class="card-body">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>ID</th>
                                            <th>名称</th>
                                            <th>状态</th>
                                            <th>创建时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>1</td>
                                            <td>测试项目 1</td>
                                            <td><span class="badge bg-success">正常</span></td>
                                            <td>2025-06-18</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">编辑</button>
                                                <button class="btn btn-sm btn-outline-danger">删除</button>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>2</td>
                                            <td>测试项目 2</td>
                                            <td><span class="badge bg-warning">待审核</span></td>
                                            <td>2025-06-18</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-primary">编辑</button>
                                                <button class="btn btn-sm btn-outline-danger">删除</button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 移动端侧边栏切换
        function toggleSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('show');
        }
        
        // 点击内容区域关闭侧边栏
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.querySelector('.sidebar');
                const mainContent = document.querySelector('.main-content');
                
                if (mainContent.contains(e.target) && !e.target.closest('button')) {
                    sidebar.classList.remove('show');
                }
            }
        });
    </script>
</body>
</html>
