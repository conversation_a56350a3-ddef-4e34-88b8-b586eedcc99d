#!/usr/bin/env python3
"""
财务系统测试脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import AdministrativeArea, User
from app.models_financial import AccountingSubject, FinancialVoucher, VoucherDetail, AccountPayable, PaymentRecord
from sqlalchemy import text
from datetime import date, datetime

def test_financial_system():
    """测试财务系统基本功能"""
    app = create_app()
    
    with app.app_context():
        print("=== 财务系统测试 ===\n")
        
        # 1. 检查数据库表是否存在
        print("1. 检查财务系统表结构...")
        try:
            # 检查会计科目表
            result = db.session.execute(text("SELECT COUNT(*) FROM accounting_subjects"))
            subjects_count = result.scalar()
            print(f"   ✓ 会计科目表存在，当前有 {subjects_count} 条记录")
            
            # 检查财务凭证表
            result = db.session.execute(text("SELECT COUNT(*) FROM financial_vouchers"))
            vouchers_count = result.scalar()
            print(f"   ✓ 财务凭证表存在，当前有 {vouchers_count} 条记录")
            
            # 检查应付账款表
            result = db.session.execute(text("SELECT COUNT(*) FROM account_payables"))
            payables_count = result.scalar()
            print(f"   ✓ 应付账款表存在，当前有 {payables_count} 条记录")
            
            # 检查付款记录表
            result = db.session.execute(text("SELECT COUNT(*) FROM payment_records"))
            payments_count = result.scalar()
            print(f"   ✓ 付款记录表存在，当前有 {payments_count} 条记录")
            
        except Exception as e:
            print(f"   ✗ 数据库表检查失败: {str(e)}")
            return False
        
        # 2. 检查学校区域
        print("\n2. 检查学校区域...")
        try:
            schools = AdministrativeArea.query.filter_by(level=3).all()
            if schools:
                print(f"   ✓ 找到 {len(schools)} 个学校区域")
                for school in schools[:3]:  # 只显示前3个
                    print(f"     - ID: {school.id}, 名称: {school.name}")
                if len(schools) > 3:
                    print(f"     ... 还有 {len(schools) - 3} 个学校")
            else:
                print("   ⚠ 未找到学校区域，请先创建学校")
                return False
        except Exception as e:
            print(f"   ✗ 学校区域检查失败: {str(e)}")
            return False
        
        # 3. 检查会计科目初始化情况
        print("\n3. 检查会计科目初始化情况...")
        try:
            for school in schools[:2]:  # 检查前2个学校
                school_subjects = AccountingSubject.query.filter_by(
                    area_id=school.id, 
                    is_system=True
                ).count()
                print(f"   学校 {school.name}: {school_subjects} 个系统科目")
                
                if school_subjects == 0:
                    print(f"   ⚠ 学校 {school.name} 尚未初始化会计科目")
                    print("   建议执行: migrations/financial_basic_subjects.sql")
                
        except Exception as e:
            print(f"   ✗ 会计科目检查失败: {str(e)}")
        
        # 4. 测试财务模型基本功能
        print("\n4. 测试财务模型...")
        try:
            if schools:
                test_school = schools[0]
                print(f"   使用学校: {test_school.name} (ID: {test_school.id})")
                
                # 检查是否有系统科目
                system_subjects = AccountingSubject.query.filter_by(
                    area_id=test_school.id,
                    is_system=True
                ).all()
                
                if system_subjects:
                    print(f"   ✓ 该学校有 {len(system_subjects)} 个系统科目")
                    
                    # 显示几个主要科目
                    main_subjects = AccountingSubject.query.filter_by(
                        area_id=test_school.id,
                        level=1
                    ).limit(5).all()
                    
                    for subject in main_subjects:
                        print(f"     - {subject.code}: {subject.name} ({subject.subject_type})")
                else:
                    print("   ⚠ 该学校没有系统科目，需要先初始化")
                
        except Exception as e:
            print(f"   ✗ 财务模型测试失败: {str(e)}")
        
        # 5. 检查权限配置
        print("\n5. 检查权限配置...")
        try:
            from app.utils.permissions import PERMISSIONS
            
            financial_modules = [
                '财务管理', '会计科目管理', '财务凭证管理', 
                '应付账款管理', '财务报表'
            ]
            
            for module in financial_modules:
                if module in PERMISSIONS:
                    actions = list(PERMISSIONS[module]['actions'].keys())
                    print(f"   ✓ {module}: {len(actions)} 个权限操作")
                else:
                    print(f"   ✗ {module}: 权限未配置")
                    
        except Exception as e:
            print(f"   ✗ 权限配置检查失败: {str(e)}")
        
        # 6. 检查路由注册
        print("\n6. 检查路由注册...")
        try:
            from flask import url_for
            
            financial_routes = [
                'financial.reports_index',
                'financial.accounting_subjects_index',
                'financial.vouchers_index',
                'financial.payables_index',
                'financial.payments_index'
            ]
            
            for route in financial_routes:
                try:
                    url = url_for(route)
                    print(f"   ✓ {route}: {url}")
                except Exception:
                    print(f"   ✗ {route}: 路由未注册")
                    
        except Exception as e:
            print(f"   ✗ 路由检查失败: {str(e)}")
        
        # 7. 检查模块可见性设置
        print("\n7. 检查模块可见性设置...")
        try:
            from app.models_visibility import ModuleVisibility
            from app.models import Role

            financial_modules = [
                'financial', 'financial_overview', 'accounting_subjects',
                'financial_vouchers', 'account_payables', 'payment_records'
            ]

            roles = Role.query.limit(3).all()
            if roles:
                for role in roles:
                    visible_count = 0
                    for module_id in financial_modules:
                        if ModuleVisibility.get_visibility(module_id, role.id):
                            visible_count += 1

                    print(f"   角色 {role.name}: {visible_count}/{len(financial_modules)} 个财务模块可见")
            else:
                print("   ⚠ 未找到角色数据")

        except Exception as e:
            print(f"   ✗ 模块可见性检查失败: {str(e)}")

        # 8. 检查角色财务权限
        print("\n8. 检查角色财务权限...")
        try:
            from app.utils.permissions import parse_permissions_json

            roles = Role.query.limit(3).all()
            financial_permission_modules = [
                '财务管理', '会计科目管理', '财务凭证管理', '应付账款管理'
            ]

            for role in roles:
                try:
                    permissions = parse_permissions_json(role.permissions or '{}')
                    financial_modules_count = sum(1 for module in financial_permission_modules if module in permissions)
                    print(f"   角色 {role.name}: {financial_modules_count}/{len(financial_permission_modules)} 个财务权限模块")
                except Exception:
                    print(f"   角色 {role.name}: 权限解析失败")

        except Exception as e:
            print(f"   ✗ 角色权限检查失败: {str(e)}")

        print("\n=== 测试完成 ===")
        print("\n📋 部署建议:")
        print("1. 如果学校没有会计科目，请执行: migrations/financial_basic_subjects.sql")
        print("2. 如果模块可见性未设置，请执行: migrations/financial_module_visibility_init.sql")
        print("3. 如果角色权限未配置，请执行: python migrations/financial_permissions_init.py")
        print("4. 访问 /financial/reports 查看财务管理首页")
        print("5. 建议先设置会计科目，再创建财务凭证")

        return True

if __name__ == '__main__':
    test_financial_system()
