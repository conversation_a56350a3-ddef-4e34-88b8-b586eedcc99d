#!/usr/bin/env python3
"""
JavaScript CSP违规自动修复工具
自动修复JS文件中的eval()和Function构造器等CSP违规代码
"""

import os
import re
import shutil
from pathlib import Path

class JSCSPFixer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root)
        self.backup_dir = self.project_root / "js_csp_backup"
        self.fixed_files = []
        self.errors = []
        
    def create_backup(self):
        """创建备份目录"""
        if self.backup_dir.exists():
            shutil.rmtree(self.backup_dir)
        self.backup_dir.mkdir(parents=True)
        print(f"✅ 创建备份目录: {self.backup_dir}")
    
    def backup_file(self, file_path):
        """备份单个文件"""
        relative_path = file_path.relative_to(self.project_root)
        backup_path = self.backup_dir / relative_path
        backup_path.parent.mkdir(parents=True, exist_ok=True)
        shutil.copy2(file_path, backup_path)
        return backup_path
    
    def fix_universal_event_handler(self):
        """修复universal-event-handler.js"""
        file_path = self.project_root / "app" / "static" / "js" / "universal-event-handler.js"
        
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 备份原文件
            backup_path = self.backup_file(file_path)
            
            # 创建安全的替代版本
            new_content = '''/**
 * 通用事件处理器 - CSP安全版本
 * 处理从内联事件迁移过来的事件处理，不使用eval()或Function构造器
 */

(function() {
    'use strict';
    
    // 预定义的安全函数映射
    const safeFunctions = {
        // 窗口操作
        'window.print()': () => window.print(),
        'window.close()': () => window.close(),
        'window.history.back()': () => window.history.back(),
        'history.back()': () => history.back(),
        'location.reload()': () => location.reload(),
        
        // 确认对话框
        'confirm': (message) => confirm(message),
        
        // 表单操作
        'resetForm()': () => {
            if (typeof resetForm === 'function') resetForm();
        },
        
        // 通用函数调用
        'goToDate': (event) => {
            if (typeof goToDate === 'function') goToDate(event);
        }
    };
    
    // 安全执行函数
    function safeExecute(funcCall) {
        // 清理函数调用
        funcCall = funcCall.trim();
        
        // 处理简单的函数调用
        if (safeFunctions[funcCall]) {
            return safeFunctions[funcCall]();
        }
        
        // 处理带参数的函数调用
        const funcMatch = funcCall.match(/^(\w+)\((.*)\)$/);
        if (funcMatch) {
            const funcName = funcMatch[1];
            const args = funcMatch[2];
            
            if (funcName === 'confirm' && args) {
                // 处理confirm调用
                const message = args.replace(/['"]/g, '');
                return confirm(message);
            }
            
            // 检查全局函数是否存在
            if (typeof window[funcName] === 'function') {
                try {
                    // 简单参数解析（仅支持字符串和数字）
                    const parsedArgs = args.split(',').map(arg => {
                        arg = arg.trim();
                        if (arg.startsWith('"') || arg.startsWith("'")) {
                            return arg.slice(1, -1);
                        }
                        if (!isNaN(arg)) {
                            return Number(arg);
                        }
                        return arg;
                    });
                    return window[funcName](...parsedArgs);
                } catch (e) {
                    console.warn('函数调用失败:', funcName, e);
                }
            }
        }
        
        // 处理return语句
        if (funcCall.startsWith('return ')) {
            const returnValue = funcCall.substring(7);
            return safeExecute(returnValue);
        }
        
        console.warn('不支持的函数调用:', funcCall);
        return false;
    }
    
    // 等待 DOM 加载完成
    document.addEventListener('DOMContentLoaded', function() {
        
        // 处理通用 onclick 事件
        document.querySelectorAll('[data-onclick]').forEach(function(element) {
            const funcCall = element.getAttribute('data-onclick');
            element.addEventListener('click', function(e) {
                e.preventDefault();
                try {
                    safeExecute(funcCall);
                } catch (error) {
                    console.error('事件处理器执行失败:', error, '函数:', funcCall);
                }
            });
        });
        
        // 处理表单提交事件
        document.querySelectorAll('[data-onsubmit]').forEach(function(form) {
            const funcCall = form.getAttribute('data-onsubmit');
            form.addEventListener('submit', function(e) {
                try {
                    const result = safeExecute(funcCall);
                    if (result === false) {
                        e.preventDefault();
                    }
                } catch (error) {
                    console.error('表单提交处理器执行失败:', error);
                    e.preventDefault();
                }
            });
        });
        
        // 处理其他事件类型
        const eventTypes = ['change', 'focus', 'blur', 'mouseover', 'mouseout', 'load'];
        eventTypes.forEach(function(eventType) {
            const selector = '[data-on' + eventType + ']';
            document.querySelectorAll(selector).forEach(function(element) {
                const funcCall = element.getAttribute('data-on' + eventType);
                element.addEventListener(eventType, function(e) {
                    try {
                        safeExecute(funcCall);
                    } catch (error) {
                        console.error('事件处理器执行失败:', error, '函数:', funcCall);
                    }
                });
            });
        });
        
        console.log('✅ 通用事件处理器已加载 (CSP安全版本)');
    });
})();'''
            
            # 写入新内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            self.fixed_files.append({
                'file': file_path,
                'backup': backup_path,
                'type': 'complete_rewrite'
            })
            
            print(f"✅ 修复文件: {file_path.relative_to(self.project_root)} (完全重写)")
            
        except Exception as e:
            error_msg = f"修复文件 {file_path} 时出错: {str(e)}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
    
    def fix_comprehensive_event_handler(self):
        """修复comprehensive-event-handler.js"""
        file_path = self.project_root / "app" / "static" / "js" / "comprehensive-event-handler.js"
        
        if not file_path.exists():
            return
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 备份原文件
            backup_path = self.backup_file(file_path)
            
            # 替换eval()调用
            content = re.sub(
                r'eval\(([^)]+)\)',
                r'safeExecuteCode(\1)',
                content
            )
            
            # 添加安全执行函数
            safe_function = '''
    // 安全执行代码的函数（替代eval）
    function safeExecuteCode(code) {
        if (!code) return;
        
        code = code.trim();
        
        // 处理confirm调用
        if (code.includes('confirm(')) {
            const confirmMatch = code.match(/confirm\\s*\\(\\s*['"](.*?)['"]\\s*\\)/);
            if (confirmMatch) {
                return confirm(confirmMatch[1]);
            }
        }
        
        // 处理简单的函数调用
        const funcMatch = code.match(/^(\\w+)\\s*\\(([^)]*)\\)$/);
        if (funcMatch) {
            const funcName = funcMatch[1];
            if (typeof window[funcName] === 'function') {
                try {
                    return window[funcName]();
                } catch (e) {
                    console.warn('函数执行失败:', funcName, e);
                }
            }
        }
        
        console.warn('不支持的代码执行:', code);
        return false;
    }
'''
            
            # 在文件开头添加安全函数
            content = content.replace(
                'document.addEventListener(\'DOMContentLoaded\', function() {',
                safe_function + '\n    document.addEventListener(\'DOMContentLoaded\', function() {'
            )
            
            # 写入修复后的内容
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.fixed_files.append({
                'file': file_path,
                'backup': backup_path,
                'type': 'eval_replacement'
            })
            
            print(f"✅ 修复文件: {file_path.relative_to(self.project_root)} (替换eval)")
            
        except Exception as e:
            error_msg = f"修复文件 {file_path} 时出错: {str(e)}"
            self.errors.append(error_msg)
            print(f"❌ {error_msg}")
    
    def fix_critical_handlers(self):
        """修复critical相关的处理器文件"""
        files_to_fix = [
            "critical-event-handler.js",
            "critical-handler-simple.js"
        ]
        
        for filename in files_to_fix:
            file_path = self.project_root / "app" / "static" / "js" / filename
            
            if not file_path.exists():
                continue
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 备份原文件
                backup_path = self.backup_file(file_path)
                
                # 替换eval()和Function构造器
                content = re.sub(r'eval\(([^)]+)\)', r'safeExecute(\1)', content)
                content = re.sub(r'new Function\(([^)]+)\)', r'createSafeFunction(\1)', content)
                
                # 添加安全替代函数
                safe_functions = '''
    // 安全执行函数（替代eval）
    function safeExecute(code) {
        if (!code) return;
        code = code.trim();
        
        // 处理confirm调用
        if (code.includes('confirm(')) {
            const confirmMatch = code.match(/confirm\\s*\\(\\s*['"](.*?)['"]\\s*\\)/);
            if (confirmMatch) {
                return confirm(confirmMatch[1]);
            }
        }
        
        console.warn('不支持的代码执行:', code);
        return false;
    }
    
    // 安全函数创建器（替代Function构造器）
    function createSafeFunction(code) {
        return function() {
            return safeExecute(code);
        };
    }
'''
                
                # 在适当位置插入安全函数
                if 'document.addEventListener' in content:
                    content = content.replace(
                        'document.addEventListener',
                        safe_functions + '\n    document.addEventListener'
                    )
                
                # 写入修复后的内容
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                self.fixed_files.append({
                    'file': file_path,
                    'backup': backup_path,
                    'type': 'function_replacement'
                })
                
                print(f"✅ 修复文件: {file_path.relative_to(self.project_root)} (替换Function构造器)")
                
            except Exception as e:
                error_msg = f"修复文件 {file_path} 时出错: {str(e)}"
                self.errors.append(error_msg)
                print(f"❌ {error_msg}")
    
    def run(self):
        """运行修复工具"""
        print("🔧 JavaScript CSP违规自动修复工具")
        print("=" * 50)
        
        # 创建备份
        self.create_backup()
        
        # 修复各个文件
        self.fix_universal_event_handler()
        self.fix_comprehensive_event_handler()
        self.fix_critical_handlers()
        
        # 输出结果
        print("\n" + "=" * 50)
        print("🎉 修复完成!")
        print(f"✅ 成功修复: {len(self.fixed_files)} 个文件")
        print(f"❌ 修复失败: {len(self.errors)} 个文件")
        
        if self.fixed_files:
            print(f"\n📦 备份位置: {self.backup_dir}")
            print("\n修复的文件:")
            for item in self.fixed_files:
                rel_path = item['file'].relative_to(self.project_root)
                print(f"  - {rel_path} ({item['type']})")
        
        if self.errors:
            print("\n❌ 错误:")
            for error in self.errors:
                print(f"  - {error}")
        
        print(f"\n💡 提示:")
        print(f"  1. 重启应用程序以应用更改")
        print(f"  2. 测试所有功能确保正常工作")
        print(f"  3. 如果有问题，可以从 {self.backup_dir} 恢复文件")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='JavaScript CSP违规自动修复工具')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    
    args = parser.parse_args()
    
    fixer = JSCSPFixer(args.project_root)
    fixer.run()

if __name__ == "__main__":
    main()
