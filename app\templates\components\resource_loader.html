<!-- 资源加载组件 - 按需加载，减少重复 -->

<!-- 基础资源 - 所有页面都需要 -->
{% macro load_base_resources() %}
<!-- jQuery -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery/jquery.min.js') }}?v=1.0.0"></script>
<!-- Bootstrap -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap.bundle.min.js') }}?v=1.0.0"></script>
<!-- 主题切换器 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/theme-switcher-simple.js') }}?v=1.0.0"></script>
<!-- 基础工具 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/main.js') }}?v=1.0.2"></script>
{% endmacro %}

<!-- 表格相关资源 -->
{% macro load_table_resources() %}
<!-- DataTables -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/datatables/datatables-zh-CN.js') }}?v=1.0.0"></script>
<!-- Bootstrap Table -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table.min.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/bootstrap-table/bootstrap-table-zh-CN.js') }}?v=1.0.0"></script>
{% endmacro %}

<!-- 表单相关资源 -->
{% macro load_form_resources() %}
<!-- Select2 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2.min.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/select2/select2-zh-CN.js') }}?v=1.0.0"></script>
<!-- 日期选择器 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/js/jquery-ui.min.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui/jquery-ui-zh-CN.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/datepicker-zh-CN.js') }}?v=1.0.0"></script>
<!-- 表单验证 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/form-validation-zh-CN.js') }}?v=1.0.0"></script>
{% endmacro %}

<!-- 图表相关资源 -->
{% macro load_chart_resources() %}
<!-- Chart.js -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/chart-js/chart-zh-CN.js') }}?v=1.0.0"></script>
{% endmacro %}

<!-- 通知相关资源 -->
{% macro load_notification_resources() %}
<!-- Toastr -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr.min.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/toastr/toastr-zh-CN.js') }}?v=1.0.0"></script>
<!-- SweetAlert2 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/sweetalert2/sweetalert2-zh-CN.js') }}?v=1.0.0"></script>
{% endmacro %}

<!-- 文件上传相关资源 -->
{% macro load_upload_resources() %}
<!-- 图片上传器 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/enhanced-image-uploader.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/file-upload-fix.js') }}?v=1.0.2"></script>
{% endmacro %}

<!-- 移动端相关资源 -->
{% macro load_mobile_resources() %}
<!-- 移动端表格转卡片 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-table-cards.js') }}?v=1.0.0"></script>
<!-- 移动端增强功能 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/mobile-enhancements.js') }}?v=2.0.0"></script>
<!-- 触摸支持 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jquery-ui-touch-punch/jquery.ui.touch-punch.min.js') }}?v=1.0.0"></script>
{% endmacro %}

<!-- 时间相关资源 -->
{% macro load_datetime_resources() %}
<!-- Moment.js -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment.min.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/moment/moment-zh-CN.js') }}?v=1.0.0"></script>
{% endmacro %}

<!-- 国际化相关资源 -->
{% macro load_i18n_resources() %}
<!-- 国际化 -->
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='bootstrap/js/bootstrap-zh-CN.js') }}?v=1.0.0"></script>
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/i18n.js') }}?v=1.0.0"></script>
{% endmacro %}

<!-- 根据页面类型加载资源 -->
{% macro load_page_resources(page_type) %}
{{ load_base_resources() }}

{% if page_type in ['list', 'index', 'table'] %}
    {{ load_table_resources() }}
    {{ load_mobile_resources() }}
{% endif %}

{% if page_type in ['form', 'create', 'edit'] %}
    {{ load_form_resources() }}
    {{ load_datetime_resources() }}
    {{ load_notification_resources() }}
{% endif %}

{% if page_type in ['dashboard', 'chart', 'statistics'] %}
    {{ load_chart_resources() }}
    {{ load_datetime_resources() }}
{% endif %}

{% if page_type in ['upload', 'media'] %}
    {{ load_upload_resources() }}
    {{ load_notification_resources() }}
{% endif %}

<!-- 通用资源 -->
{{ load_i18n_resources() }}
{% endmacro %}
