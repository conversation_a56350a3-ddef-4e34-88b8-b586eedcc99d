-- 优化周菜单查询性能
PRINT '开始优化周菜单查询性能...'
PRINT '========================================'

-- 问题分析：
-- 1. 查询使用了 CONVERT(VARCHAR(10), week_start, 120) 函数，导致索引失效
-- 2. 需要为 weekly_menus 表创建合适的索引

PRINT '问题分析：'
PRINT '• 查询使用了日期转换函数，导致索引失效'
PRINT '• weekly_menus 表缺少针对 area_id + week_start 的索引'
PRINT '• 存在重复查询问题'
PRINT ''

-- 1. 检查 weekly_menus 表当前的索引
PRINT '1. 检查 weekly_menus 表当前索引：'
SELECT 
    i.name AS index_name,
    i.type_desc AS index_type,
    STUFF((
        SELECT ', ' + c.name
        FROM sys.index_columns ic
        INNER JOIN sys.columns c ON ic.object_id = c.object_id AND ic.column_id = c.column_id
        WHERE ic.object_id = i.object_id AND ic.index_id = i.index_id AND ic.is_included_column = 0
        ORDER BY ic.key_ordinal
        FOR XML PATH('')
    ), 1, 2, '') AS key_columns
FROM sys.indexes i
WHERE i.object_id = OBJECT_ID('weekly_menus')
    AND i.name IS NOT NULL
ORDER BY i.name

PRINT ''
PRINT '2. 创建周菜单查询优化索引：'

-- 创建针对周菜单查询的复合索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_week_start')
BEGIN
    CREATE NONCLUSTERED INDEX IX_weekly_menus_area_week_start
    ON weekly_menus (area_id, week_start)
    INCLUDE (id, week_end, status, created_by, created_at, updated_at)
    PRINT '✓ 创建周菜单区域周开始日期索引'
END
ELSE
    PRINT '- 周菜单区域周开始日期索引已存在'

-- 创建针对状态查询的索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_status_area')
BEGIN
    CREATE NONCLUSTERED INDEX IX_weekly_menus_status_area
    ON weekly_menus (status, area_id)
    INCLUDE (id, week_start, week_end, created_by)
    PRINT '✓ 创建周菜单状态区域索引'
END
ELSE
    PRINT '- 周菜单状态区域索引已存在'

PRINT ''
PRINT '3. 建议的查询优化：'
PRINT ''
PRINT '原查询（慢）：'
PRINT 'SELECT TOP 1 * FROM weekly_menus'
PRINT 'WHERE area_id = :area_id AND CONVERT(VARCHAR(10), week_start, 120) = :week_start_str'
PRINT ''
PRINT '优化后查询（快）：'
PRINT 'SELECT TOP 1 * FROM weekly_menus'
PRINT 'WHERE area_id = :area_id AND week_start = :week_start_date'
PRINT ''
PRINT '或者使用日期范围查询：'
PRINT 'SELECT TOP 1 * FROM weekly_menus'
PRINT 'WHERE area_id = :area_id'
PRINT '  AND week_start >= :week_start_date'
PRINT '  AND week_start < DATEADD(day, 1, :week_start_date)'

PRINT ''
PRINT '========================================'
PRINT '索引创建完成！'
PRINT '========================================'
PRINT ''
PRINT '📊 优化效果：'
PRINT '• 为 area_id + week_start 创建了复合索引'
PRINT '• 包含了查询需要的所有字段，避免回表'
PRINT '• 支持高效的日期范围查询'
PRINT ''
PRINT '🚀 建议的应用层优化：'
PRINT '1. 修改查询逻辑，避免使用 CONVERT 函数'
PRINT '2. 直接使用日期类型进行比较'
PRINT '3. 考虑添加查询缓存'
PRINT '4. 减少重复查询'
PRINT ''
PRINT '💡 查询优化建议：'
PRINT '• 将字符串日期转换为 datetime 类型后查询'
PRINT '• 使用参数化查询避免类型转换'
PRINT '• 考虑在应用层缓存周菜单数据'

-- 更新统计信息
PRINT ''
PRINT '正在更新统计信息...'
UPDATE STATISTICS weekly_menus
PRINT '✓ 统计信息更新完成'

PRINT ''
PRINT '🎉 周菜单查询优化完成！'
PRINT ''
PRINT '现在请测试周菜单相关功能的性能。'
PRINT '如果仍然较慢，建议修改应用代码中的查询逻辑。'
