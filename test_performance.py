#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本
测试应用启动速度和数据库连接性能
"""

import time
import sys
import os
from datetime import datetime

def test_import_speed():
    """测试模块导入速度"""
    print("=== 模块导入速度测试 ===")
    
    start_time = time.time()
    
    # 测试主要模块导入
    try:
        from app import create_app
        import_time_1 = time.time() - start_time
        print(f"✓ 导入 create_app: {import_time_1:.3f}秒")
        
        start_time = time.time()
        from config import Config
        import_time_2 = time.time() - start_time
        print(f"✓ 导入 Config: {import_time_2:.3f}秒")
        
        start_time = time.time()
        from performance_config import DevelopmentPerformanceConfig
        import_time_3 = time.time() - start_time
        print(f"✓ 导入 PerformanceConfig: {import_time_3:.3f}秒")
        
        total_import_time = import_time_1 + import_time_2 + import_time_3
        print(f"总导入时间: {total_import_time:.3f}秒")
        
        return total_import_time
        
    except Exception as e:
        print(f"✗ 模块导入失败: {str(e)}")
        return None

def test_app_creation_speed():
    """测试应用创建速度"""
    print("\n=== 应用创建速度测试 ===")
    
    try:
        from app import create_app
        from config import Config
        from performance_config import DevelopmentPerformanceConfig
        
        # 测试标准配置
        start_time = time.time()
        app1 = create_app(Config)
        standard_time = time.time() - start_time
        print(f"✓ 标准配置创建应用: {standard_time:.3f}秒")
        
        # 测试性能配置
        start_time = time.time()
        app2 = create_app(DevelopmentPerformanceConfig)
        performance_time = time.time() - start_time
        print(f"✓ 性能配置创建应用: {performance_time:.3f}秒")
        
        improvement = ((standard_time - performance_time) / standard_time) * 100
        if improvement > 0:
            print(f"性能提升: {improvement:.1f}%")
        else:
            print(f"性能差异: {abs(improvement):.1f}% (性能配置稍慢)")
        
        return standard_time, performance_time
        
    except Exception as e:
        print(f"✗ 应用创建失败: {str(e)}")
        return None, None

def test_database_connection_speed():
    """测试数据库连接速度"""
    print("\n=== 数据库连接速度测试 ===")
    
    try:
        from app import create_app, db
        from config import Config
        from performance_config import DevelopmentPerformanceConfig
        
        # 测试标准配置数据库连接
        app1 = create_app(Config)
        with app1.app_context():
            start_time = time.time()
            result = db.session.execute(db.text("SELECT 1"))
            result.fetchone()
            standard_db_time = time.time() - start_time
            print(f"✓ 标准配置数据库连接: {standard_db_time:.3f}秒")
        
        # 测试性能配置数据库连接
        app2 = create_app(DevelopmentPerformanceConfig)
        with app2.app_context():
            start_time = time.time()
            result = db.session.execute(db.text("SELECT 1"))
            result.fetchone()
            performance_db_time = time.time() - start_time
            print(f"✓ 性能配置数据库连接: {performance_db_time:.3f}秒")
        
        improvement = ((standard_db_time - performance_db_time) / standard_db_time) * 100
        if improvement > 0:
            print(f"数据库连接性能提升: {improvement:.1f}%")
        else:
            print(f"数据库连接性能差异: {abs(improvement):.1f}%")
        
        return standard_db_time, performance_db_time
        
    except Exception as e:
        print(f"✗ 数据库连接测试失败: {str(e)}")
        return None, None

def test_multiple_requests():
    """测试多次请求性能"""
    print("\n=== 多次数据库查询性能测试 ===")
    
    try:
        from app import create_app, db
        from performance_config import DevelopmentPerformanceConfig
        
        app = create_app(DevelopmentPerformanceConfig)
        
        with app.app_context():
            # 预热连接
            db.session.execute(db.text("SELECT 1")).fetchone()
            
            # 测试多次查询
            num_queries = 10
            start_time = time.time()
            
            for i in range(num_queries):
                result = db.session.execute(db.text("SELECT @@VERSION"))
                result.fetchone()
            
            total_time = time.time() - start_time
            avg_time = total_time / num_queries
            
            print(f"✓ {num_queries}次查询总时间: {total_time:.3f}秒")
            print(f"✓ 平均每次查询时间: {avg_time:.3f}秒")
            print(f"✓ 每秒查询数 (QPS): {1/avg_time:.1f}")
            
            return total_time, avg_time
        
    except Exception as e:
        print(f"✗ 多次请求测试失败: {str(e)}")
        return None, None

def generate_performance_report():
    """生成性能报告"""
    print("\n" + "=" * 60)
    print("性能优化总结报告")
    print("=" * 60)
    
    print("\n已实施的优化措施:")
    print("1. ✓ 关闭调试模式 (DEBUG = False)")
    print("2. ✓ 优化数据库连接池配置")
    print("3. ✓ 关闭SQL日志输出")
    print("4. ✓ 增加连接池大小 (30个连接)")
    print("5. ✓ 延长连接回收时间 (2小时)")
    print("6. ✓ 关闭模板自动重载")
    print("7. ✓ 启用简单内存缓存")
    print("8. ✓ 优化日志级别 (WARNING)")
    print("9. ✓ 关闭不必要的功能")
    
    print("\n使用建议:")
    print("• 使用 'python run_fast.py' 启动高性能模式")
    print("• 标准模式: 'python run.py'")
    print("• 如需调试，临时启用DEBUG模式")
    
    print("\n性能监控:")
    print("• 监控数据库连接池使用情况")
    print("• 定期检查应用内存使用")
    print("• 观察页面响应时间")

def main():
    """主函数"""
    print("应用性能测试开始...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 测试模块导入速度
    import_time = test_import_speed()
    
    # 2. 测试应用创建速度
    standard_time, performance_time = test_app_creation_speed()
    
    # 3. 测试数据库连接速度
    standard_db_time, performance_db_time = test_database_connection_speed()
    
    # 4. 测试多次请求性能
    total_time, avg_time = test_multiple_requests()
    
    # 5. 生成性能报告
    generate_performance_report()
    
    print("\n" + "=" * 60)
    print("测试完成!")
    
    if all([import_time, standard_time, performance_time]):
        print("✓ 所有测试通过，性能优化配置可用")
    else:
        print("❌ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
