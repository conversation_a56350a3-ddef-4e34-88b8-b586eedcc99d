# xiaoyuanst.com 域名配置完成说明

## 配置概述

已成功将服务器域名从 `tdtech.xin` 更新为 `xiaoyuanst.com`，并修复了 HTTP 500.50 URL Rewrite 错误。

### 服务器信息
- **域名**: xiaoyuanst.com
- **IP地址**: **************
- **Flask端口**: 8080
- **IIS端口**: 80

## 已完成的修改

### 1. 域名配置文件更新
- ✅ `setup_iis_site_fixed.ps1` - 更新域名绑定
- ✅ `setup_iis_site.ps1` - 更新域名绑定
- ✅ `服务器配置指南.md` - 更新域名信息
- ✅ `proxy_server.py` - 更新域名引用
- ✅ `setup_firewall.ps1` - 更新防火墙规则

### 2. URL Rewrite 错误修复
- ✅ `web.config` - 添加 `allowedServerVariables` 配置
- ✅ 创建 `fix_url_rewrite_error.ps1` - 自动修复脚本
- ✅ 创建 `web.config.fixed` - 修复后的配置模板

### 3. 新增配置脚本
- ✅ `setup_xiaoyuanst_domain.ps1` - 完整的域名配置脚本
- ✅ `test_xiaoyuanst_domain.ps1` - 配置测试脚本

### 4. 文档更新
- ✅ `修复URL_Rewrite错误指南.md` - 错误修复指南
- ✅ `xiaoyuanst.com域名配置说明.md` - 域名配置说明

## 使用方法

### 快速配置（推荐）

以管理员身份运行PowerShell，执行以下命令：

```powershell
# 1. 完整配置域名访问
.\setup_xiaoyuanst_domain.ps1

# 2. 如果遇到500.50错误，运行修复脚本
.\fix_url_rewrite_error.ps1

# 3. 测试配置
.\test_xiaoyuanst_domain.ps1

# 4. 启动Flask应用
python run.py
```

### 访问地址

配置完成后，可通过以下地址访问：

- **本地访问**: http://localhost
- **IP访问**: http://**************
- **域名访问**: http://xiaoyuanst.com
- **带www访问**: http://www.xiaoyuanst.com

## 架构说明

```
外网用户 → xiaoyuanst.com:80 → IIS反向代理 → Flask应用:8080
```

### 关键配置

1. **IIS站点配置**
   - 站点名称: xiaoyuanst.com
   - 应用程序池: xiaoyuanst.com_AppPool
   - 域名绑定: xiaoyuanst.com, www.xiaoyuanst.com

2. **URL Rewrite配置**
   - 代理目标: http://127.0.0.1:8080
   - 允许的服务器变量: HTTP_X_FORWARDED_FOR, HTTP_X_FORWARDED_PROTO, HTTP_X_FORWARDED_HOST

3. **防火墙配置**
   - 端口80: HTTP访问
   - 端口8080: Flask应用（仅本地）
   - 端口443: HTTPS（预留）

## 故障排除

### 常见问题及解决方案

#### 1. HTTP 500.50 错误
```powershell
# 运行修复脚本
.\fix_url_rewrite_error.ps1
```

#### 2. 域名无法访问
- 检查DNS记录是否指向 **************
- 确认防火墙允许端口80访问
- 验证IIS站点正在运行

#### 3. 502错误
- 确认Flask应用在端口8080上运行
- 检查web.config配置
- 查看IIS日志

#### 4. 403错误
- 检查站点权限设置
- 确认IIS_IUSRS和IUSR用户权限

### 诊断命令

```powershell
# 检查站点状态
Get-Website -Name "xiaoyuanst.com"

# 检查应用程序池状态
Get-WebAppPool -Name "xiaoyuanst.com_AppPool"

# 检查端口占用
netstat -ano | findstr ":80"
netstat -ano | findstr ":8080"

# 检查防火墙规则
Get-NetFirewallRule -DisplayName "*xiaoyuanst*"

# 重启服务
iisreset /noforce
```

## DNS配置要求

确保域名DNS记录正确配置：

```
类型: A记录
主机: @
值: **************

类型: A记录  
主机: www
值: **************
```

## 安全建议

1. **SSL证书**: 建议配置HTTPS证书
2. **防火墙**: 只开放必要端口（80, 443）
3. **访问日志**: 定期检查IIS访问日志
4. **系统更新**: 定期更新IIS和Windows系统

## 监控和维护

### 日志文件位置
- **IIS日志**: `C:\inetpub\logs\LogFiles\`
- **Flask日志**: 应用程序目录下的logs文件夹

### 性能监控
```powershell
# 检查进程状态
Get-Process -Name "w3wp" | Select-Object CPU, WorkingSet
Get-Process -Name "python" | Select-Object CPU, WorkingSet
```

## 备份和恢复

### 重要文件备份
- `web.config` - IIS配置文件
- IIS站点配置
- 应用程序文件

### 恢复步骤
1. 恢复web.config文件
2. 重新运行配置脚本
3. 重启IIS服务
4. 测试访问

## 下一步建议

1. **SSL配置**: 配置HTTPS证书以提高安全性
2. **负载均衡**: 如需要，可配置多个Flask实例
3. **监控系统**: 部署监控工具监控服务状态
4. **备份策略**: 建立定期备份机制

## 技术支持

如遇问题，请按以下顺序排查：
1. 运行测试脚本：`.\test_xiaoyuanst_domain.ps1`
2. 检查IIS日志和Windows事件日志
3. 验证Flask应用程序状态
4. 确认网络连接和DNS配置

配置完成！您现在可以通过 xiaoyuanst.com 访问您的应用程序。
