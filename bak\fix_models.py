#!/usr/bin/env python3
"""
修复models.py中的SQLAlchemy参数错误
将 primary_key=1 改为 primary_key=True
将 nullable=0 改为 nullable=False
将 nullable=1 改为 nullable=True
将 unique=1 改为 unique=True
"""

import re

def fix_models_file():
    """修复models.py文件中的参数错误"""
    
    # 读取文件内容
    with open('app/models.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 记录修改次数
    changes = 0
    
    # 修复 primary_key=1 -> primary_key=True
    pattern1 = r'primary_key=1'
    replacement1 = 'primary_key=True'
    content, count1 = re.subn(pattern1, replacement1, content)
    changes += count1
    print(f"修复 primary_key=1: {count1} 处")
    
    # 修复 nullable=0 -> nullable=False
    pattern2 = r'nullable=0'
    replacement2 = 'nullable=False'
    content, count2 = re.subn(pattern2, replacement2, content)
    changes += count2
    print(f"修复 nullable=0: {count2} 处")
    
    # 修复 nullable=1 -> nullable=True
    pattern3 = r'nullable=1'
    replacement3 = 'nullable=True'
    content, count3 = re.subn(pattern3, replacement3, content)
    changes += count3
    print(f"修复 nullable=1: {count3} 处")
    
    # 修复 unique=1 -> unique=True
    pattern4 = r'unique=1'
    replacement4 = 'unique=True'
    content, count4 = re.subn(pattern4, replacement4, content)
    changes += count4
    print(f"修复 unique=1: {count4} 处")
    
    # 修复 default=0 -> default=False (仅对Boolean字段)
    pattern5 = r'db\.Boolean.*?default=0'
    matches = re.findall(pattern5, content)
    for match in matches:
        new_match = match.replace('default=0', 'default=False')
        content = content.replace(match, new_match)
        changes += 1
    print(f"修复 Boolean default=0: {len(matches)} 处")
    
    # 修复 default=1 -> default=True (仅对Boolean字段)
    pattern6 = r'db\.Boolean.*?default=1'
    matches = re.findall(pattern6, content)
    for match in matches:
        new_match = match.replace('default=1', 'default=True')
        content = content.replace(match, new_match)
        changes += 1
    print(f"修复 Boolean default=1: {len(matches)} 处")
    
    # 写回文件
    if changes > 0:
        with open('app/models.py', 'w', encoding='utf-8') as f:
            f.write(content)
        print(f"\n总共修复了 {changes} 处错误")
        print("models.py 文件已更新")
    else:
        print("没有发现需要修复的错误")

if __name__ == '__main__':
    print("开始修复 models.py 文件...")
    fix_models_file()
    print("修复完成！")
