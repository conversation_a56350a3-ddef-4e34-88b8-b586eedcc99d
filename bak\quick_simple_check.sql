-- 超级简化的快速检查
PRINT '快速检查数据库状态...'

-- 1. 检查关键表是否存在（最重要）
PRINT '1. 检查关键表：'
SELECT 
    'weekly_menus' AS table_name,
    CASE WHEN OBJECT_ID('weekly_menus') IS NOT NULL THEN '存在' ELSE '缺失' END AS status
UNION ALL
SELECT 
    'weekly_menu_recipes',
    CASE WHEN OBJECT_ID('weekly_menu_recipes') IS NOT NULL THEN '存在' ELSE '缺失' END
UNION ALL
SELECT 
    'food_samples',
    CASE WHEN OBJECT_ID('food_samples') IS NOT NULL THEN '存在' ELSE '缺失' END

-- 2. 检查我们创建的索引数量
PRINT ''
PRINT '2. 检查我们创建的索引：'
SELECT COUNT(*) AS our_indexes_count
FROM sys.indexes 
WHERE name LIKE '%safe%' OR name LIKE '%fixed%' OR name LIKE '%final%' OR name LIKE '%optimized%'

-- 3. 简单测试查询性能
PRINT ''
PRINT '3. 测试关键查询：'
DECLARE @start_time DATETIME = GETDATE()

-- 测试周菜单查询
SELECT TOP 1 id FROM weekly_menus WHERE area_id = 42

DECLARE @end_time DATETIME = GETDATE()
DECLARE @duration_ms INT = DATEDIFF(millisecond, @start_time, @end_time)

PRINT '周菜单查询耗时: ' + CAST(@duration_ms AS VARCHAR) + ' 毫秒'

PRINT ''
PRINT '快速检查完成！'
PRINT ''
IF @duration_ms > 1000
    PRINT '⚠️ 查询较慢，建议优化'
ELSE
    PRINT '✅ 查询速度正常'
