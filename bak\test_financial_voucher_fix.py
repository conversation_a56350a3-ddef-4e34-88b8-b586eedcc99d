#!/usr/bin/env python3
"""
测试财务凭证生成的修复
验证根据食材分类动态选择会计科目的功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import *
from app.models_financial import *
from sqlalchemy import text
from decimal import Decimal

def test_category_mapping():
    """测试食材分类与会计科目的映射"""
    app = create_app()
    
    with app.app_context():
        print("=== 测试食材分类与会计科目映射 ===")
        
        # 1. 检查会计科目是否存在
        print("\n1. 检查会计科目:")
        subject_codes = ['1201', '120101', '120102', '120103', '120104', '120105', '120106', '2201']
        
        for code in subject_codes:
            subject = AccountingSubject.query.filter_by(code=code).first()
            if subject:
                print(f"  ✓ {code}: {subject.name} (area_id: {subject.area_id})")
            else:
                print(f"  ✗ {code}: 未找到")
        
        # 2. 检查食材分类
        print("\n2. 检查食材分类:")
        categories = IngredientCategory.query.all()
        for cat in categories:
            print(f"  - {cat.name}: {cat.description}")
        
        # 3. 检查食材与分类的关联
        print("\n3. 检查食材与分类关联:")
        ingredients_with_category = db.session.execute(text("""
            SELECT TOP 10 i.name, ic.name as category_name
            FROM ingredients i
            LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
        """)).fetchall()
        
        for row in ingredients_with_category:
            print(f"  - {row[0]}: {row[1] or '无分类'}")
        
        # 4. 测试分类映射逻辑
        print("\n4. 测试分类映射逻辑:")
        category_to_code_map = {
            '蔬菜类': '120101',
            '肉类': '120102',
            '水产类': '120103',
            '粮油类': '120104',
            '调料类': '120105',
            '冷冻食品': '120106',
            '蔬菜': '120101',
            '肉': '120102',
            '水产': '120103',
            '粮油': '120104',
            '调料': '120105',
            '调味品': '120105'
        }
        
        for category_name, expected_code in category_to_code_map.items():
            subject = AccountingSubject.query.filter_by(code=expected_code).first()
            if subject:
                print(f"  ✓ {category_name} -> {expected_code}: {subject.name}")
            else:
                print(f"  ✗ {category_name} -> {expected_code}: 科目不存在")

def test_stock_in_category_analysis():
    """测试入库单的食材分类分析"""
    app = create_app()
    
    with app.app_context():
        print("\n=== 测试入库单食材分类分析 ===")
        
        # 查找一个有明细的入库单
        stock_in = db.session.execute(text("""
            SELECT TOP 1 si.id, si.stock_in_number, si.total_cost
            FROM stock_ins si
            WHERE EXISTS (
                SELECT 1 FROM stock_in_items sii WHERE sii.stock_in_id = si.id
            )
            ORDER BY si.id DESC
        """)).fetchone()
        
        if not stock_in:
            print("  ✗ 没有找到包含明细的入库单")
            return
        
        print(f"\n测试入库单: {stock_in.stock_in_number} (ID: {stock_in.id})")
        print(f"总金额: {stock_in.total_cost}")
        
        # 分析食材分类
        category_results = db.session.execute(text("""
            SELECT 
                ic.name as category_name,
                COUNT(*) as item_count,
                SUM(sii.quantity * sii.unit_price) as category_total_cost,
                STRING_AGG(i.name, ', ') as ingredients
            FROM stock_in_items sii
            JOIN ingredients i ON sii.ingredient_id = i.id
            LEFT JOIN ingredient_categories ic ON i.category_id = ic.id
            WHERE sii.stock_in_id = :stock_in_id
            GROUP BY ic.name
        """), {'stock_in_id': stock_in.id}).fetchall()
        
        print(f"\n食材分类分析:")
        total_check = 0
        for row in category_results:
            category_name = row.category_name or '无分类'
            item_count = row.item_count
            category_total = float(row.category_total_cost or 0)
            ingredients = row.ingredients
            
            print(f"  - {category_name}:")
            print(f"    项目数: {item_count}")
            print(f"    金额: {category_total:.2f}")
            print(f"    食材: {ingredients}")
            
            total_check += category_total
        
        print(f"\n金额校验: {total_check:.2f} vs {float(stock_in.total_cost):.2f}")
        if abs(total_check - float(stock_in.total_cost)) < 0.01:
            print("  ✓ 金额校验通过")
        else:
            print("  ✗ 金额校验失败")

if __name__ == '__main__':
    print("开始测试财务凭证修复...")
    test_category_mapping()
    test_stock_in_category_analysis()
    print("\n测试完成!")
