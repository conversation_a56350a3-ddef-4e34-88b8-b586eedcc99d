/* 左右式布局增强样式 - 使用系统主题变量 */

/* 左侧导航栏样式优化 - 使用系统主题 */
.sidebar {
    width: 200px !important;
    min-width: 200px !important;
    background: var(--theme-primary, var(--bs-primary, #007bff));
    color: var(--theme-surface, var(--bs-light, #f8f9fa));
    transition: background-color 0.3s ease, color 0.3s ease;
}

.sidebar-header {
    background: var(--theme-primary, var(--bs-primary, #007bff));
    transition: background 0.3s ease;
    border-bottom: 1px solid var(--theme-primary-dark, var(--bs-primary-dark, #0056b3));
}

.sidebar-brand {
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    text-decoration: none;
    transition: color 0.3s ease;
}

.sidebar-brand:hover {
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    text-decoration: none;
}

.sidebar-nav .nav-link {
    color: var(--theme-surface, var(--bs-light, #f8f9fa));
    text-decoration: none;
    transition: all 0.3s ease;
    position: relative;
}

.sidebar-nav .nav-link:hover {
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    text-decoration: none;
    background: var(--theme-primary-dark, var(--bs-primary-dark, #0056b3));
}

.sidebar-nav .nav-link.active {
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    background: var(--theme-primary-dark, var(--bs-primary-dark, #0056b3));
    text-decoration: none;
}

/* 主题预览样式 */
.theme-preview {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid rgba(255,255,255,0.3);
}

.theme-preview.primary { background: #007bff; }
.theme-preview.secondary { background: #6c757d; }
.theme-preview.success { background: #28a745; }
.theme-preview.warning { background: #ffc107; }
.theme-preview.info { background: #17a2b8; }
.theme-preview.danger { background: #dc3545; }

/* 通知样式 */
.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.notification-content {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 4px;
}

.notification-time {
    font-size: 0.75rem;
    color: #999;
}

/* 侧边栏滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
    width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* 内容区域滚动条样式 */
.content-area::-webkit-scrollbar {
    width: 8px;
}

.content-area::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.content-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.content-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 右侧顶部工具栏样式优化 - 使用系统主题变量 */
.top-toolbar {
    background: var(--theme-primary, var(--bs-primary, #007bff)) !important;
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    border-bottom: 1px solid var(--theme-primary-dark, var(--bs-primary-dark, #0056b3));
    transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.top-toolbar h5 {
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    font-weight: 600;
    margin: 0;
    transition: color 0.3s ease;
}

.toolbar-left {
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
}

.toolbar-right .btn {
    margin-left: 0.25rem;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    border-color: var(--theme-primary-dark, var(--bs-primary-dark, #0056b3));
    background: var(--theme-primary-dark, var(--bs-primary-dark, #0056b3));
    transition: all 0.3s ease;
}

.toolbar-right .btn:hover {
    background: var(--theme-primary-light, var(--bs-primary-light, #66b3ff));
    border-color: var(--theme-primary-light, var(--bs-primary-light, #66b3ff));
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    transform: translateY(-1px);
}

.toolbar-right .dropdown-toggle::after {
    display: none;
}

/* 移动端切换按钮样式 */
.mobile-toggle {
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
    border-color: var(--theme-primary-dark, var(--bs-primary-dark, #0056b3)) !important;
    background: var(--theme-primary-dark, var(--bs-primary-dark, #0056b3)) !important;
}

.mobile-toggle:hover {
    background: var(--theme-primary-light, var(--bs-primary-light, #66b3ff)) !important;
    border-color: var(--theme-primary-light, var(--bs-primary-light, #66b3ff)) !important;
    color: var(--theme-surface, var(--bs-light, #f8f9fa)) !important;
}

/* 移动端遮罩层 */
@media (max-width: 768px) {
    .sidebar.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 200px;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: -1;
    }
}

/* 平滑过渡效果 */
.sidebar,
.main-content,
.sidebar-nav .nav-link,
.top-toolbar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 侧边栏子菜单动画 */
.sidebar-nav .collapse {
    transition: height 0.3s ease;
}

.sidebar-nav .fa-chevron-down {
    transition: transform 0.3s ease;
}

/* 主题色适配 - 使用现有的系统主题变量 */
/* 注意：主题变量已在 theme-colors.css 中定义，这里只需要确保正确引用 */

/* 侧边栏主题适配 - 使用系统主题变量 */
.sidebar {
    background: var(--theme-primary, var(--bs-primary, #007bff)) !important;
    transition: background-color 0.3s ease;
}

.sidebar-header {
    background: linear-gradient(135deg,
        var(--theme-primary, var(--bs-primary, #007bff)),
        var(--theme-primary-dark, var(--bs-primary-dark, #0056b3))
    ) !important;
    transition: background 0.3s ease;
}

/* 顶部工具栏主题适配 - 使用系统主题变量 */
.top-toolbar {
    background: var(--theme-primary, var(--bs-primary, #007bff)) !important;
    transition: background-color 0.3s ease;
}

/* 响应式字体大小 */
@media (max-width: 1200px) {
    .sidebar-brand {
        font-size: 1rem;
    }
    
    .sidebar-nav .nav-link {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 992px) {
    .top-toolbar {
        padding: 0.5rem 1rem;
    }
    
    .toolbar-right .btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
}

/* 右侧内容区域优化 */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #f8f9fa;
    transition: background-color 0.3s ease;
}

.content-area {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    background: #ffffff;
    transition: background-color 0.3s ease;
    position: relative;
}

/* 确保顶部固定，内容自适应 */
.content-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(0,0,0,0.1), transparent);
}

/* 卡片样式优化 */
.content-area .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border-radius: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.content-area .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

/* 表格样式优化 */
.content-area .table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

/* 按钮样式优化 */
.content-area .btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.content-area .btn:hover {
    transform: translateY(-1px);
}

/* 表单样式优化 */
.content-area .form-control {
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.content-area .form-control:focus {
    border-color: var(--sidebar-bg, #007bff);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 页面标题样式 */
.top-toolbar h5 {
    color: #333;
    font-weight: 600;
    margin: 0;
}

/* 加载动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-area > * {
    animation: fadeIn 0.3s ease-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .content-area {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: #ffffff;
    }
    
    .top-toolbar {
        background: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .top-toolbar h5 {
        color: #ffffff;
    }
    
    .content-area .card {
        background: #2d2d2d;
        color: #ffffff;
    }
    
    .content-area .table {
        background: #2d2d2d;
        color: #ffffff;
    }
    
    .content-area .form-control {
        background: #404040;
        border-color: #555555;
        color: #ffffff;
    }
}
