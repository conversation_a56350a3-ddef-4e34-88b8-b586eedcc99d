/* 左右式布局增强样式 */

/* 主题预览样式 */
.theme-preview {
    display: inline-block;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    margin-right: 8px;
    border: 2px solid rgba(255,255,255,0.3);
}

.theme-preview.primary { background: #007bff; }
.theme-preview.secondary { background: #6c757d; }
.theme-preview.success { background: #28a745; }
.theme-preview.warning { background: #ffc107; }
.theme-preview.info { background: #17a2b8; }
.theme-preview.danger { background: #dc3545; }

/* 通知样式 */
.notification-item {
    padding: 12px 16px;
    border-bottom: 1px solid #f0f0f0;
    transition: background-color 0.2s ease;
}

.notification-item:hover {
    background-color: #f8f9fa;
}

.notification-item.unread {
    background-color: #e3f2fd;
    border-left: 3px solid #2196f3;
}

.notification-title {
    font-weight: 600;
    font-size: 0.9rem;
    margin-bottom: 4px;
}

.notification-content {
    font-size: 0.8rem;
    color: #666;
    margin-bottom: 4px;
}

.notification-time {
    font-size: 0.75rem;
    color: #999;
}

/* 侧边栏滚动条样式 */
.sidebar-nav::-webkit-scrollbar {
    width: 6px;
}

.sidebar-nav::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

.sidebar-nav::-webkit-scrollbar-thumb {
    background: rgba(255,255,255,0.3);
    border-radius: 3px;
}

.sidebar-nav::-webkit-scrollbar-thumb:hover {
    background: rgba(255,255,255,0.5);
}

/* 内容区域滚动条样式 */
.content-area::-webkit-scrollbar {
    width: 8px;
}

.content-area::-webkit-scrollbar-track {
    background: #f1f1f1;
}

.content-area::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.content-area::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 工具栏按钮样式 */
.toolbar-right .btn {
    margin-left: 0.25rem;
    border-radius: 6px;
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
}

.toolbar-right .dropdown-toggle::after {
    display: none;
}

/* 移动端遮罩层 */
@media (max-width: 768px) {
    .sidebar.show::before {
        content: '';
        position: fixed;
        top: 0;
        left: 200px;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        z-index: -1;
    }
}

/* 平滑过渡效果 */
.sidebar,
.main-content,
.sidebar-nav .nav-link,
.top-toolbar {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 侧边栏子菜单动画 */
.sidebar-nav .collapse {
    transition: height 0.3s ease;
}

.sidebar-nav .fa-chevron-down {
    transition: transform 0.3s ease;
}

/* 主题色适配 */
[data-theme="primary"] {
    --sidebar-bg: #007bff;
    --sidebar-bg-dark: #0056b3;
}

[data-theme="secondary"] {
    --sidebar-bg: #6c757d;
    --sidebar-bg-dark: #545b62;
}

[data-theme="success"] {
    --sidebar-bg: #28a745;
    --sidebar-bg-dark: #1e7e34;
}

[data-theme="warning"] {
    --sidebar-bg: #ffc107;
    --sidebar-bg-dark: #d39e00;
}

[data-theme="info"] {
    --sidebar-bg: #17a2b8;
    --sidebar-bg-dark: #117a8b;
}

[data-theme="danger"] {
    --sidebar-bg: #dc3545;
    --sidebar-bg-dark: #bd2130;
}

/* 使用CSS变量的侧边栏样式 */
.sidebar {
    background: var(--sidebar-bg, #007bff);
}

.sidebar-header {
    background: linear-gradient(135deg, var(--sidebar-bg, #007bff), var(--sidebar-bg-dark, #0056b3));
}

/* 响应式字体大小 */
@media (max-width: 1200px) {
    .sidebar-brand {
        font-size: 1rem;
    }
    
    .sidebar-nav .nav-link {
        font-size: 0.85rem;
        padding: 0.6rem 0.8rem;
    }
}

@media (max-width: 992px) {
    .top-toolbar {
        padding: 0.5rem 1rem;
    }
    
    .toolbar-right .btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.8rem;
    }
}

/* 内容区域优化 */
.content-area {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
}

/* 卡片样式优化 */
.content-area .card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border-radius: 12px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.content-area .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
}

/* 表格样式优化 */
.content-area .table {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.06);
}

/* 按钮样式优化 */
.content-area .btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.content-area .btn:hover {
    transform: translateY(-1px);
}

/* 表单样式优化 */
.content-area .form-control {
    border-radius: 6px;
    border: 1px solid #e0e0e0;
    transition: border-color 0.2s ease, box-shadow 0.2s ease;
}

.content-area .form-control:focus {
    border-color: var(--sidebar-bg, #007bff);
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 页面标题样式 */
.top-toolbar h5 {
    color: #333;
    font-weight: 600;
    margin: 0;
}

/* 加载动画 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.content-area > * {
    animation: fadeIn 0.3s ease-out;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .content-area {
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        color: #ffffff;
    }
    
    .top-toolbar {
        background: #2d2d2d;
        border-bottom-color: #404040;
    }
    
    .top-toolbar h5 {
        color: #ffffff;
    }
    
    .content-area .card {
        background: #2d2d2d;
        color: #ffffff;
    }
    
    .content-area .table {
        background: #2d2d2d;
        color: #ffffff;
    }
    
    .content-area .form-control {
        background: #404040;
        border-color: #555555;
        color: #ffffff;
    }
}
