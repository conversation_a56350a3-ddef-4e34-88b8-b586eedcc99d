-- 更新 consumption_plans 表结构以适配新的周菜单系统
-- 移除对旧 menu_plan_id 的依赖，确保与 weekly_menus 系统兼容
USE [StudentsCMSSP]
GO

PRINT '开始更新 consumption_plans 表结构...'
PRINT '========================================'

-- 1. 检查 consumption_plans 表是否存在
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'consumption_plans')
BEGIN
    PRINT '✓ consumption_plans 表存在'
    
    -- 检查是否有 menu_plan_id 字段（旧字段）
    IF EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'menu_plan_id')
    BEGIN
        PRINT '⚠️ 发现旧的 menu_plan_id 字段，需要处理...'
        
        -- 检查该字段是否有外键约束
        IF EXISTS (SELECT * FROM sys.foreign_keys WHERE parent_object_id = OBJECT_ID('consumption_plans') AND name LIKE '%menu_plan%')
        BEGIN
            PRINT '删除 menu_plan_id 相关的外键约束...'
            DECLARE @fk_name NVARCHAR(128)
            SELECT @fk_name = name FROM sys.foreign_keys 
            WHERE parent_object_id = OBJECT_ID('consumption_plans') AND name LIKE '%menu_plan%'
            
            IF @fk_name IS NOT NULL
            BEGIN
                EXEC('ALTER TABLE consumption_plans DROP CONSTRAINT ' + @fk_name)
                PRINT '✓ 删除外键约束: ' + @fk_name
            END
        END
        
        -- 删除 menu_plan_id 字段
        ALTER TABLE consumption_plans DROP COLUMN menu_plan_id
        PRINT '✓ 删除 menu_plan_id 字段'
    END
    ELSE
    BEGIN
        PRINT '✓ 没有发现 menu_plan_id 字段'
    END
    
    -- 检查是否有 area_id 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'area_id')
    BEGIN
        PRINT '添加 area_id 字段...'
        ALTER TABLE consumption_plans ADD area_id INT NOT NULL DEFAULT 1
        
        -- 添加外键约束
        ALTER TABLE consumption_plans ADD CONSTRAINT FK_consumption_plans_area_id 
        FOREIGN KEY (area_id) REFERENCES administrative_areas(id)
        
        PRINT '✓ 添加 area_id 字段和外键约束'
    END
    ELSE
    BEGIN
        PRINT '✓ area_id 字段已存在'
    END
    
    -- 检查其他必要字段
    PRINT ''
    PRINT '检查其他必要字段...'
    
    -- 检查 consumption_date 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'consumption_date')
    BEGIN
        ALTER TABLE consumption_plans ADD consumption_date DATE NOT NULL DEFAULT GETDATE()
        PRINT '✓ 添加 consumption_date 字段'
    END
    
    -- 检查 meal_type 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'meal_type')
    BEGIN
        ALTER TABLE consumption_plans ADD meal_type NVARCHAR(20) NOT NULL DEFAULT '午餐'
        PRINT '✓ 添加 meal_type 字段'
    END
    
    -- 检查 status 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'status')
    BEGIN
        ALTER TABLE consumption_plans ADD status NVARCHAR(20) NOT NULL DEFAULT '计划中'
        PRINT '✓ 添加 status 字段'
    END
    
    -- 检查 created_by 字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'created_by')
    BEGIN
        ALTER TABLE consumption_plans ADD created_by INT NOT NULL DEFAULT 1
        ALTER TABLE consumption_plans ADD CONSTRAINT FK_consumption_plans_created_by 
        FOREIGN KEY (created_by) REFERENCES users(id)
        PRINT '✓ 添加 created_by 字段和外键约束'
    END
    
    -- 检查时间戳字段
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'created_at')
    BEGIN
        ALTER TABLE consumption_plans ADD created_at DATETIME2(1) NOT NULL DEFAULT GETDATE()
        PRINT '✓ 添加 created_at 字段'
    END
    
    IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'updated_at')
    BEGIN
        ALTER TABLE consumption_plans ADD updated_at DATETIME2(1) NOT NULL DEFAULT GETDATE()
        PRINT '✓ 添加 updated_at 字段'
    END
    
END
ELSE
BEGIN
    PRINT '❌ consumption_plans 表不存在，需要创建...'
    
    CREATE TABLE [dbo].[consumption_plans](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [consumption_date] [date] NOT NULL,
        [meal_type] [nvarchar](20) NOT NULL,
        [diners_count] [int] NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '计划中',
        [created_by] [int] NOT NULL,
        [approved_by] [int] NULL,
        [notes] [ntext] NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        [updated_at] [datetime2](1) NOT NULL DEFAULT GETDATE(),
        CONSTRAINT [PK_consumption_plans] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_consumption_plans_area_id] FOREIGN KEY([area_id]) 
            REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_consumption_plans_created_by] FOREIGN KEY([created_by]) 
            REFERENCES [dbo].[users] ([id]),
        CONSTRAINT [FK_consumption_plans_approved_by] FOREIGN KEY([approved_by]) 
            REFERENCES [dbo].[users] ([id])
    )
    
    PRINT '✓ consumption_plans 表创建成功'
END

-- 2. 创建必要的索引
PRINT ''
PRINT '创建索引...'

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'IX_consumption_plans_area_id')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_consumption_plans_area_id] ON [dbo].[consumption_plans] ([area_id])
    PRINT '✓ 创建 consumption_plans.area_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'IX_consumption_plans_consumption_date')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_consumption_plans_consumption_date] ON [dbo].[consumption_plans] ([consumption_date])
    PRINT '✓ 创建 consumption_plans.consumption_date 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('consumption_plans') AND name = 'IX_consumption_plans_area_date_meal')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_consumption_plans_area_date_meal]
    ON [dbo].[consumption_plans] ([area_id], [consumption_date], [meal_type])
    INCLUDE ([status], [diners_count])
    PRINT '✓ 创建 consumption_plans 复合优化索引'
END

PRINT ''
PRINT '========================================'
PRINT '🎉 consumption_plans 表更新完成！'
PRINT ''
PRINT '表结构现在与新的周菜单系统兼容：'
PRINT '• 移除了对旧 menu_plan_id 的依赖'
PRINT '• 确保有 area_id 字段用于学校级数据隔离'
PRINT '• 包含必要的时间戳和状态字段'
PRINT '• 创建了优化查询的索引'
PRINT '========================================'
