-- 重新创建周菜单相关表结构
-- 确保表结构与模型定义一致
USE [StudentsCMSSP]
GO

PRINT '开始重新创建周菜单表结构...'
PRINT '========================================'

-- 1. 检查并创建 weekly_menus 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menus')
BEGIN
    PRINT '创建 weekly_menus 表...'
    
    CREATE TABLE [dbo].[weekly_menus](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [area_id] [int] NOT NULL,
        [week_start] [date] NOT NULL,
        [week_end] [date] NOT NULL,
        [status] [nvarchar](20) NOT NULL DEFAULT '计划中',
        [created_by] [int] NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        [updated_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        CONSTRAINT [PK_weekly_menus] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_weekly_menus_area_id] FOREIGN KEY([area_id]) 
            REFERENCES [dbo].[administrative_areas] ([id]),
        CONSTRAINT [FK_weekly_menus_created_by] FOREIGN KEY([created_by]) 
            REFERENCES [dbo].[users] ([id])
    )
    
    PRINT '✓ weekly_menus 表创建成功'
END
ELSE
BEGIN
    PRINT '✓ weekly_menus 表已存在'
END

-- 2. 检查并创建 weekly_menu_recipes 表
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'weekly_menu_recipes')
BEGIN
    PRINT '创建 weekly_menu_recipes 表...'
    
    CREATE TABLE [dbo].[weekly_menu_recipes](
        [id] [int] IDENTITY(1,1) NOT NULL,
        [weekly_menu_id] [int] NOT NULL,
        [day_of_week] [int] NOT NULL,
        [meal_type] [nvarchar](20) NOT NULL,
        [recipe_id] [int] NULL,
        [recipe_name] [nvarchar](100) NOT NULL,
        [created_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        [updated_at] [datetime2](1) NOT NULL DEFAULT (GETDATE()),
        CONSTRAINT [PK_weekly_menu_recipes] PRIMARY KEY CLUSTERED ([id] ASC),
        CONSTRAINT [FK_weekly_menu_recipes_weekly_menu_id] FOREIGN KEY([weekly_menu_id]) 
            REFERENCES [dbo].[weekly_menus] ([id]) ON DELETE CASCADE,
        CONSTRAINT [FK_weekly_menu_recipes_recipe_id] FOREIGN KEY([recipe_id]) 
            REFERENCES [dbo].[recipes] ([id])
    )
    
    PRINT '✓ weekly_menu_recipes 表创建成功'
END
ELSE
BEGIN
    PRINT '✓ weekly_menu_recipes 表已存在'
END

-- 3. 创建必要的索引
PRINT ''
PRINT '创建索引...'

-- weekly_menus 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_id')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_area_id] ON [dbo].[weekly_menus] ([area_id])
    PRINT '✓ 创建 weekly_menus.area_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_week_start')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_week_start] ON [dbo].[weekly_menus] ([week_start])
    PRINT '✓ 创建 weekly_menus.week_start 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_status')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_status] ON [dbo].[weekly_menus] ([status])
    PRINT '✓ 创建 weekly_menus.status 索引'
END

-- 复合索引用于优化常用查询
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menus') AND name = 'IX_weekly_menus_area_week_optimized')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menus_area_week_optimized]
    ON [dbo].[weekly_menus] ([area_id], [week_start])
    INCLUDE ([id], [week_end], [status], [created_by], [created_at], [updated_at])
    PRINT '✓ 创建 weekly_menus 复合优化索引'
END

-- weekly_menu_recipes 表索引
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_weekly_menu_id')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menu_recipes_weekly_menu_id] ON [dbo].[weekly_menu_recipes] ([weekly_menu_id])
    PRINT '✓ 创建 weekly_menu_recipes.weekly_menu_id 索引'
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_recipe_id')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menu_recipes_recipe_id] ON [dbo].[weekly_menu_recipes] ([recipe_id])
    PRINT '✓ 创建 weekly_menu_recipes.recipe_id 索引'
END

-- 复合索引用于优化查询
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE object_id = OBJECT_ID('weekly_menu_recipes') AND name = 'IX_weekly_menu_recipes_menu_day_meal')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_weekly_menu_recipes_menu_day_meal]
    ON [dbo].[weekly_menu_recipes] ([weekly_menu_id], [day_of_week], [meal_type])
    INCLUDE ([recipe_id], [recipe_name])
    PRINT '✓ 创建 weekly_menu_recipes 复合优化索引'
END

PRINT ''
PRINT '========================================'
PRINT '🎉 周菜单表结构创建完成！'
PRINT ''
PRINT '已创建的表：'
PRINT '1. weekly_menus - 周菜单主表'
PRINT '2. weekly_menu_recipes - 周菜单食谱关联表'
PRINT ''
PRINT '已创建的索引：'
PRINT '• weekly_menus: area_id, week_start, status, 复合优化索引'
PRINT '• weekly_menu_recipes: weekly_menu_id, recipe_id, 复合优化索引'
PRINT ''
PRINT '现在可以正常使用周菜单功能了！'
PRINT '========================================'
