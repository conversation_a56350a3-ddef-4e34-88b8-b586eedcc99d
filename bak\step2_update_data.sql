-- 第二步：更新 stock_ins 表的数据
-- 请在执行 step1_add_fields_only.sql 之后执行此脚本

USE [StudentsCMSSP]
GO

PRINT '开始更新 stock_ins 表的数据...'

-- 设置所有入库单的 area_id 为 42（您当前学校的ID）
UPDATE stock_ins 
SET area_id = 42 
WHERE area_id IS NULL;

DECLARE @updated_area_count INT = @@ROWCOUNT;
PRINT '✓ 更新了 ' + CAST(@updated_area_count AS VARCHAR(10)) + ' 条记录的 area_id'

-- 计算 total_cost（从入库明细计算）
UPDATE si
SET total_cost = ISNULL((
    SELECT SUM(ISNULL(sii.unit_price, 0) * ISNULL(sii.quantity, 0))
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
), 0)
FROM stock_ins si
WHERE si.total_cost IS NULL OR si.total_cost = 0;

DECLARE @updated_cost_count INT = @@ROWCOUNT;
PRINT '✓ 计算了 ' + CAST(@updated_cost_count AS VARCHAR(10)) + ' 条记录的 total_cost'

-- 设置 supplier_id（从第一个入库明细获取）
UPDATE si
SET supplier_id = (
    SELECT TOP 1 sii.supplier_id
    FROM stock_in_items sii
    WHERE sii.stock_in_id = si.id
    AND sii.supplier_id IS NOT NULL
)
FROM stock_ins si
WHERE si.supplier_id IS NULL;

DECLARE @updated_supplier_count INT = @@ROWCOUNT;
PRINT '✓ 设置了 ' + CAST(@updated_supplier_count AS VARCHAR(10)) + ' 条记录的 supplier_id'

-- 显示更新结果
SELECT 
    COUNT(*) as 入库单总数,
    COUNT(area_id) as 有area_id的记录数,
    COUNT(supplier_id) as 有supplier_id的记录数,
    COUNT(total_cost) as 有total_cost的记录数,
    SUM(CASE WHEN is_financial_confirmed = 1 THEN 1 ELSE 0 END) as 已财务确认数量
FROM stock_ins;

PRINT '数据更新完成！'
PRINT '请继续执行 step3_create_indexes.sql'

GO
