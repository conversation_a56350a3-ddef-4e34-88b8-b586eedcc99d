-- =============================================
-- 修复财务权限键名不一致问题
-- 将英文键名统一改为中文键名
-- =============================================

USE [StudentsCMSSP]
GO

PRINT '开始修复财务权限键名...'
PRINT '========================================'

-- =============================================
-- 修复财务主管角色权限
-- =============================================

PRINT '修复财务主管角色权限...'

UPDATE roles 
SET permissions = '{
    "财务管理": ["view", "create", "edit", "approve", "audit"],
    "会计科目管理": ["view", "create", "edit"],
    "财务凭证管理": ["view", "create", "edit", "review", "post"],
    "应付账款管理": ["view", "create", "edit", "payment", "approve"],
    "收入管理": ["view", "create", "edit", "review"],
    "成本核算": ["view", "calculate", "review", "adjust"],
    "财务报表": ["view", "export", "print", "config"],
    "库存管理": ["view", "approve"],
    "采购管理": ["view", "approve"]
}'
WHERE name = '财务主管'

PRINT '  ✓ 财务主管权限修复完成'

-- =============================================
-- 修复财务专员角色权限
-- =============================================

PRINT '修复财务专员角色权限...'

UPDATE roles 
SET permissions = '{
    "财务管理": ["view", "create", "edit"],
    "会计科目管理": ["view"],
    "财务凭证管理": ["view", "create", "edit"],
    "应付账款管理": ["view", "create", "edit"],
    "收入管理": ["view", "create", "edit"],
    "成本核算": ["view", "calculate"],
    "财务报表": ["view", "export"],
    "库存管理": ["view"],
    "采购管理": ["view"]
}'
WHERE name = '财务专员'

PRINT '  ✓ 财务专员权限修复完成'

-- =============================================
-- 修复出纳员角色权限
-- =============================================

PRINT '修复出纳员角色权限...'

UPDATE roles 
SET permissions = '{
    "财务管理": ["view"],
    "财务凭证管理": ["view"],
    "应付账款管理": ["view", "payment"],
    "收入管理": ["view", "create", "edit"],
    "成本核算": ["view"],
    "财务报表": ["view"],
    "库存管理": ["view"],
    "采购管理": ["view"]
}'
WHERE name = '出纳员'

PRINT '  ✓ 出纳员权限修复完成'

-- =============================================
-- 检查学校管理员权限
-- =============================================

PRINT '检查学校管理员权限...'

DECLARE @school_admin_current NVARCHAR(MAX)
SELECT @school_admin_current = permissions FROM roles WHERE name = '学校管理员'

-- 如果学校管理员权限中没有财务权限，则添加
IF @school_admin_current NOT LIKE '%"财务管理"%'
BEGIN
    DECLARE @school_admin_new NVARCHAR(MAX)
    SELECT @school_admin_new = ISNULL(permissions, '{}') FROM roles WHERE name = '学校管理员'
    
    IF @school_admin_new = '{}' OR @school_admin_new IS NULL OR @school_admin_new = ''
    BEGIN
        SET @school_admin_new = '{
            "财务管理": ["view", "approve"],
            "会计科目管理": ["view"],
            "财务凭证管理": ["view", "review"],
            "应付账款管理": ["view", "approve"],
            "收入管理": ["view", "review"],
            "成本核算": ["view", "review"],
            "财务报表": ["view", "export", "print"]
        }'
    END
    ELSE
    BEGIN
        SET @school_admin_new = REPLACE(@school_admin_new, '}', ',
            "财务管理": ["view", "approve"],
            "会计科目管理": ["view"],
            "财务凭证管理": ["view", "review"],
            "应付账款管理": ["view", "approve"],
            "收入管理": ["view", "review"],
            "成本核算": ["view", "review"],
            "财务报表": ["view", "export", "print"]
        }')
    END
    
    UPDATE roles SET permissions = @school_admin_new WHERE name = '学校管理员'
    PRINT '  ✓ 学校管理员财务权限已添加'
END
ELSE
BEGIN
    PRINT '  - 学校管理员已有财务权限'
END

-- =============================================
-- 检查食堂管理员权限
-- =============================================

PRINT '检查食堂管理员权限...'

DECLARE @cafeteria_admin_current NVARCHAR(MAX)
SELECT @cafeteria_admin_current = permissions FROM roles WHERE name = '食堂管理员'

-- 如果食堂管理员权限中没有财务权限，则添加
IF @cafeteria_admin_current NOT LIKE '%"财务管理"%'
BEGIN
    DECLARE @cafeteria_admin_new NVARCHAR(MAX)
    SELECT @cafeteria_admin_new = ISNULL(permissions, '{}') FROM roles WHERE name = '食堂管理员'
    
    IF @cafeteria_admin_new = '{}' OR @cafeteria_admin_new IS NULL OR @cafeteria_admin_new = ''
    BEGIN
        SET @cafeteria_admin_new = '{
            "财务管理": ["view"],
            "成本核算": ["view"],
            "财务报表": ["view"]
        }'
    END
    ELSE
    BEGIN
        SET @cafeteria_admin_new = REPLACE(@cafeteria_admin_new, '}', ',
            "财务管理": ["view"],
            "成本核算": ["view"],
            "财务报表": ["view"]
        }')
    END
    
    UPDATE roles SET permissions = @cafeteria_admin_new WHERE name = '食堂管理员'
    PRINT '  ✓ 食堂管理员财务权限已添加'
END
ELSE
BEGIN
    PRINT '  - 食堂管理员已有财务权限'
END

-- =============================================
-- 最终验证
-- =============================================

PRINT ''
PRINT '权限修复完成！最终验证结果：'
PRINT '========================================'

SELECT
    name AS '角色名称',
    description AS '角色描述',
    CASE
        WHEN permissions LIKE '%"财务管理"%'
        THEN '有财务权限'
        ELSE '无财务权限'
    END AS '财务权限状态',
    CASE
        WHEN permissions LIKE '%"会计科目管理"%'
        THEN '有会计科目权限'
        ELSE '无会计科目权限'
    END AS '会计科目权限状态',
    LEN(permissions) AS '权限JSON长度'
FROM roles
WHERE name IN ('系统管理员', '超级管理员', '学校管理员', '财务专员', '财务主管', '出纳员', '食堂管理员')
ORDER BY
    CASE name
        WHEN '系统管理员' THEN 1
        WHEN '超级管理员' THEN 2
        WHEN '财务主管' THEN 3
        WHEN '财务专员' THEN 4
        WHEN '出纳员' THEN 5
        WHEN '学校管理员' THEN 6
        WHEN '食堂管理员' THEN 7
        ELSE 8
    END

-- 显示具体的财务权限内容
PRINT ''
PRINT '各角色的财务权限详情：'
PRINT '========================================'

SELECT 
    name AS '角色名称',
    CASE 
        WHEN permissions LIKE '%"财务管理"%' THEN
            SUBSTRING(permissions, 
                CHARINDEX('"财务管理"', permissions), 
                CHARINDEX(']', permissions, CHARINDEX('"财务管理"', permissions)) - CHARINDEX('"财务管理"', permissions) + 1
            )
        ELSE '无财务管理权限'
    END AS '财务管理权限'
FROM roles
WHERE name IN ('系统管理员', '超级管理员', '学校管理员', '财务专员', '财务主管', '出纳员', '食堂管理员')
AND permissions LIKE '%"财务管理"%'

PRINT ''
PRINT '财务权限键名修复完成！'
PRINT '========================================'
