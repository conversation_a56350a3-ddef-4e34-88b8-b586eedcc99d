#!/usr/bin/env python3
"""
测试财务凭证审核路由是否正确注册
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app
from flask import url_for

def test_financial_routes():
    """测试财务路由"""
    app = create_app()
    
    with app.app_context():
        print("🔍 测试财务凭证审核路由...")
        
        # 测试现有路由
        routes_to_test = [
            ('financial.vouchers_index', {}, '凭证列表'),
            ('financial.review_voucher', {'id': 1}, '审核凭证'),
            ('financial.reject_voucher', {'id': 1}, '拒绝凭证'),
            ('financial.batch_review_vouchers', {}, '批量审核'),
            ('financial.view_voucher', {'id': 1}, '查看凭证'),
            ('financial.edit_voucher', {'id': 1}, '编辑凭证'),
        ]
        
        success_count = 0
        total_count = len(routes_to_test)
        
        for endpoint, params, description in routes_to_test:
            try:
                url = url_for(endpoint, **params)
                print(f"  ✅ {description}: {url}")
                success_count += 1
            except Exception as e:
                print(f"  ❌ {description}: {e}")
        
        print(f"\n📊 测试结果: {success_count}/{total_count} 个路由正常")
        
        if success_count == total_count:
            print("🎉 所有财务审核路由都已正确注册！")
            return True
        else:
            print("⚠️  部分路由注册失败，请重启Flask应用")
            return False

if __name__ == '__main__':
    test_financial_routes()
