"""
添加缺失字段到purchase_orders表
"""

import pyodbc
import os
from dotenv import load_dotenv
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 加载环境变量
load_dotenv()

def add_missing_columns():
    """向purchase_orders表添加缺失的列"""
    try:
        # 获取数据库连接信息
        server = os.getenv('DB_SERVER', '14.103.246.164')
        database = os.getenv('DB_NAME', 'StudentsCMSSP')
        username = os.getenv('DB_USER', 'StudentsCMSSP')
        password = os.getenv('DB_PASSWORD', 'Xg2LS44Cyz5Zt8')

        # 创建连接字符串 - 使用SQL Server身份验证
        conn_str = f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}'

        # 连接数据库
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()

        # 需要添加的列定义
        columns_to_add = [
            ('confirmed_at', 'DATETIME2(1) NULL'),
            ('delivered_at', 'DATETIME2(1) NULL'),
            ('delivery_notes', 'NVARCHAR(MAX) NULL'),
            ('cancelled_at', 'DATETIME2(1) NULL'),
            ('cancel_reason', 'NVARCHAR(MAX) NULL'),
            ('notes', 'NVARCHAR(MAX) NULL')
        ]

        for column_name, column_definition in columns_to_add:
            # 检查列是否已存在
            cursor.execute("""
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'purchase_orders'
            AND COLUMN_NAME = ?
            """, column_name)

            column_exists = cursor.fetchone()[0] > 0

            if not column_exists:
                # 添加列
                alter_sql = f"ALTER TABLE purchase_orders ADD {column_name} {column_definition}"
                cursor.execute(alter_sql)
                conn.commit()
                logger.info(f"成功添加{column_name}列到purchase_orders表")
            else:
                logger.info(f"{column_name}列已存在于purchase_orders表中")

        # 关闭连接
        cursor.close()
        conn.close()

        return True
    except Exception as e:
        logger.error(f"添加列时出错: {str(e)}")
        return False

if __name__ == "__main__":
    add_missing_columns()
