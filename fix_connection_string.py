#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复连接字符串格式
"""

import pyodbc
from urllib.parse import quote_plus

def test_connection_string_formats():
    """测试不同的连接字符串格式"""
    print("=== 测试连接字符串格式 ===")
    
    server = "14.103.246.164"
    database = "StudentsCMSSP"
    username = "StudentsCMSSP"
    password = "Xg2LS44Cyz5Zt8"
    
    # 不同的连接字符串格式
    formats = [
        # 格式1: 基本格式
        f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}",
        
        # 格式2: 使用端口号
        f"DRIVER={{SQL Server}};SERVER={server},1433;DATABASE={database};UID={username};PWD={password}",
        
        # 格式3: 添加超时
        f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};Connection Timeout=30",
        
        # 格式4: 使用ODBC Driver 17
        f"DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}",
        
        # 格式5: 添加加密选项
        f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};Encrypt=no",
        
        # 格式6: 信任服务器证书
        f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes",
        
        # 格式7: 组合选项
        f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};Encrypt=no;TrustServerCertificate=yes",
        
        # 格式8: 使用User ID而不是UID
        f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};User ID={username};Password={password}",
        
        # 格式9: 使用完整的参数名
        f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};User ID={username};Password={password};Encrypt=no",
    ]
    
    for i, conn_str in enumerate(formats, 1):
        print(f"\n格式 {i}:")
        print(f"连接字符串: {conn_str}")
        
        try:
            conn = pyodbc.connect(conn_str, timeout=10)
            cursor = conn.cursor()
            
            # 测试查询
            cursor.execute("SELECT @@VERSION")
            version = cursor.fetchone()[0]
            
            cursor.execute("SELECT DB_NAME()")
            db_name = cursor.fetchone()[0]
            
            print(f"✓ 连接成功!")
            print(f"  数据库: {db_name}")
            print(f"  版本: {version[:50]}...")
            
            cursor.close()
            conn.close()
            
            return conn_str
            
        except Exception as e:
            print(f"✗ 连接失败: {str(e)}")
    
    return None

def test_sqlalchemy_formats():
    """测试SQLAlchemy连接字符串格式"""
    print("\n=== 测试SQLAlchemy格式 ===")
    
    server = "14.103.246.164"
    database = "StudentsCMSSP"
    username = "StudentsCMSSP"
    password = "Xg2LS44Cyz5Zt8"
    
    # 不同的SQLAlchemy格式
    formats = [
        # 格式1: 直接URL格式
        f"mssql+pyodbc://{username}:{password}@{server}/{database}?driver=SQL+Server",
        
        # 格式2: 使用odbc_connect
        f"mssql+pyodbc:///?odbc_connect={quote_plus(f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}')}",
        
        # 格式3: 添加加密选项
        f"mssql+pyodbc:///?odbc_connect={quote_plus(f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};Encrypt=no')}",
        
        # 格式4: 使用ODBC Driver 17
        f"mssql+pyodbc:///?odbc_connect={quote_plus(f'DRIVER={{ODBC Driver 17 for SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}')}",
        
        # 格式5: 信任服务器证书
        f"mssql+pyodbc:///?odbc_connect={quote_plus(f'DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password};TrustServerCertificate=yes')}",
    ]
    
    try:
        from sqlalchemy import create_engine, text
        
        for i, uri in enumerate(formats, 1):
            print(f"\nSQLAlchemy格式 {i}:")
            print(f"URI: {uri}")
            
            try:
                engine = create_engine(uri)
                
                with engine.connect() as connection:
                    result = connection.execute(text("SELECT @@VERSION"))
                    version = result.fetchone()[0]
                    
                    result = connection.execute(text("SELECT DB_NAME()"))
                    db_name = result.fetchone()[0]
                    
                    print(f"✓ SQLAlchemy连接成功!")
                    print(f"  数据库: {db_name}")
                    print(f"  版本: {version[:50]}...")
                    
                    return uri
                    
            except Exception as e:
                print(f"✗ SQLAlchemy连接失败: {str(e)}")
        
    except ImportError:
        print("SQLAlchemy未安装，跳过测试")
    
    return None

def generate_config_update(working_conn_str, working_sqlalchemy_uri):
    """生成配置更新建议"""
    print("\n=== 配置更新建议 ===")
    
    if working_conn_str:
        print("✓ 找到可用的ODBC连接字符串:")
        print(f"  {working_conn_str}")
        
        if working_sqlalchemy_uri:
            print("\n✓ 找到可用的SQLAlchemy URI:")
            print(f"  {working_sqlalchemy_uri}")
            
            print("\n建议更新config.py:")
            print("```python")
            print("# 数据库配置")
            print("from urllib.parse import quote_plus")
            
            # 提取基础连接字符串
            if "Encrypt=no" in working_conn_str:
                base_conn_str = working_conn_str
            else:
                base_conn_str = working_conn_str + ";Encrypt=no"
            
            print(f'conn_str = "{base_conn_str}"')
            print("quoted_conn_str = quote_plus(conn_str)")
            print('SQLALCHEMY_DATABASE_URI = os.environ.get("DATABASE_URL") or f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"')
            print("```")
        else:
            print("\n❌ SQLAlchemy连接失败，需要进一步调试")
    else:
        print("❌ 所有连接格式都失败了")

def main():
    """主函数"""
    print("连接字符串格式修复测试...")
    print("=" * 60)
    
    # 测试ODBC连接字符串格式
    working_conn_str = test_connection_string_formats()
    
    # 测试SQLAlchemy格式
    working_sqlalchemy_uri = test_sqlalchemy_formats()
    
    # 生成配置更新建议
    generate_config_update(working_conn_str, working_sqlalchemy_uri)

if __name__ == "__main__":
    main()
