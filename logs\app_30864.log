2025-06-18 21:12:30,826 INFO: 应用启动 - PID: 30864 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-18 21:12:38,566 ERROR: Exception on / [GET] [in C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py:838]
Traceback (most recent call last):
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1473, in wsgi_app
    response = self.full_dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 882, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask_cors\extension.py", line 194, in wrapped_function
    return cors_after_request(app.make_response(f(*args, **kwargs)))
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 880, in full_dispatch_request
    rv = self.dispatch_request()
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 865, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "C:\StudentsCMSSP\app\main\routes.py", line 72, in index
    return render_template('main/canteen_dashboard_new.html',
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 150, in render_template
    return _render(app, template, context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\templating.py", line 131, in _render
    rv = template.render(context)
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 1301, in render
    self.environment.handle_exception()
  File "C:\StudentsCMSSP\venv\lib\site-packages\jinja2\environment.py", line 936, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "C:\StudentsCMSSP\app\templates\main\canteen_dashboard_new.html", line 824, in top-level template code
    {% include 'guide/scenario_selection.html' %}
  File "C:\StudentsCMSSP\app\templates\base.html", line 282, in top-level template code
    <a class="nav-link" href="{{ url_for(child.url) if not child.get('url_params') else get_url(child) }}" style="padding: 0.5rem 1rem; font-size: 0.85rem;">
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1084, in url_for
    return self.handle_url_build_error(error, endpoint, values)
  File "C:\StudentsCMSSP\venv\lib\site-packages\flask\app.py", line 1073, in url_for
    rv = url_adapter.build(  # type: ignore[union-attr]
  File "C:\StudentsCMSSP\venv\lib\site-packages\werkzeug\routing\map.py", line 924, in build
    raise BuildError(endpoint, values, method, self)
werkzeug.routing.exceptions.BuildError: Could not build url for endpoint 'meal_records.index'. Did you mean 'warehouse.index' instead?
