# xiaoyuanst.com 域名重定向测试脚本
# 功能：测试www和非www域名重定向是否正常工作

Write-Host "=== xiaoyuanst.com 域名重定向测试 ===" -ForegroundColor Green
Write-Host ""

# 测试函数
function Test-DomainRedirect {
    param(
        [string]$Url,
        [string]$ExpectedRedirect = $null
    )
    
    Write-Host "测试: $Url" -ForegroundColor Yellow
    
    try {
        # 使用-MaximumRedirection 0 来捕获重定向响应
        $response = Invoke-WebRequest -Uri $Url -Method Head -MaximumRedirection 0 -ErrorAction SilentlyContinue
        
        if ($response.StatusCode -eq 301 -or $response.StatusCode -eq 302) {
            $location = $response.Headers.Location
            Write-Host "  ✓ 重定向响应 - 状态码: $($response.StatusCode)" -ForegroundColor Green
            Write-Host "  ✓ 重定向到: $location" -ForegroundColor Green
            
            if ($ExpectedRedirect -and $location -ne $ExpectedRedirect) {
                Write-Host "  ⚠ 警告: 期望重定向到 $ExpectedRedirect，实际重定向到 $location" -ForegroundColor Yellow
            }
            return $true
        } elseif ($response.StatusCode -eq 200) {
            Write-Host "  ✓ 直接访问成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
            return $true
        } else {
            Write-Host "  ✗ 意外状态码: $($response.StatusCode)" -ForegroundColor Red
            return $false
        }
    } catch {
        # 尝试允许重定向的请求
        try {
            $response = Invoke-WebRequest -Uri $Url -Method Head -TimeoutSec 10 -ErrorAction Stop
            Write-Host "  ✓ 访问成功 - 状态码: $($response.StatusCode)" -ForegroundColor Green
            return $true
        } catch {
            Write-Host "  ✗ 访问失败: $($_.Exception.Message)" -ForegroundColor Red
            return $false
        }
    }
}

# 测试本地服务
Write-Host "第一步：测试本地Flask服务..." -ForegroundColor Cyan

$localTests = @(
    @{ Url = "http://localhost:8080"; Description = "Flask应用直接访问" },
    @{ Url = "http://127.0.0.1:8080"; Description = "Flask应用IP访问" }
)

$localSuccess = 0
foreach ($test in $localTests) {
    Write-Host "测试: $($test.Description)" -ForegroundColor Yellow
    if (Test-DomainRedirect -Url $test.Url) {
        $localSuccess++
    }
}

Write-Host ""
if ($localSuccess -eq $localTests.Count) {
    Write-Host "✓ 本地Flask服务运行正常" -ForegroundColor Green
} else {
    Write-Host "✗ 本地Flask服务有问题，请检查是否已启动 (python run.py)" -ForegroundColor Red
}

# 测试IIS代理
Write-Host "第二步：测试IIS反向代理..." -ForegroundColor Cyan

$proxyTests = @(
    @{ Url = "http://localhost"; Description = "IIS本地访问" },
    @{ Url = "http://127.0.0.1"; Description = "IIS IP访问" }
)

$proxySuccess = 0
foreach ($test in $proxyTests) {
    Write-Host "测试: $($test.Description)" -ForegroundColor Yellow
    if (Test-DomainRedirect -Url $test.Url) {
        $proxySuccess++
    }
}

Write-Host ""
if ($proxySuccess -eq $proxyTests.Count) {
    Write-Host "✓ IIS反向代理工作正常" -ForegroundColor Green
} else {
    Write-Host "✗ IIS反向代理有问题，请检查IIS配置" -ForegroundColor Red
}

# 测试域名访问
Write-Host "第三步：测试域名访问..." -ForegroundColor Cyan

$domainTests = @(
    @{ 
        Url = "http://xiaoyuanst.com"; 
        Description = "主域名访问";
        ExpectedRedirect = $null
    },
    @{ 
        Url = "http://www.xiaoyuanst.com"; 
        Description = "www域名访问";
        ExpectedRedirect = "http://xiaoyuanst.com/"
    }
)

$domainSuccess = 0
foreach ($test in $domainTests) {
    Write-Host "测试: $($test.Description)" -ForegroundColor Yellow
    if (Test-DomainRedirect -Url $test.Url -ExpectedRedirect $test.ExpectedRedirect) {
        $domainSuccess++
    }
}

Write-Host ""
if ($domainSuccess -eq $domainTests.Count) {
    Write-Host "✓ 域名访问和重定向工作正常" -ForegroundColor Green
} else {
    Write-Host "✗ 域名访问有问题，请检查DNS配置和IIS绑定" -ForegroundColor Red
}

# 测试重定向详情
Write-Host "第四步：详细测试www重定向..." -ForegroundColor Cyan

try {
    Write-Host "发送请求到: http://www.xiaoyuanst.com" -ForegroundColor Yellow
    $response = Invoke-WebRequest -Uri "http://www.xiaoyuanst.com" -Method Get -MaximumRedirection 0 -ErrorAction SilentlyContinue
    
    if ($response.StatusCode -eq 301) {
        Write-Host "✓ 永久重定向 (301) - 这是正确的" -ForegroundColor Green
        $location = $response.Headers.Location
        Write-Host "✓ 重定向目标: $location" -ForegroundColor Green
        
        if ($location -eq "http://xiaoyuanst.com/") {
            Write-Host "✓ 重定向目标正确" -ForegroundColor Green
        } else {
            Write-Host "⚠ 重定向目标不正确，期望: http://xiaoyuanst.com/" -ForegroundColor Yellow
        }
    } elseif ($response.StatusCode -eq 302) {
        Write-Host "⚠ 临时重定向 (302) - 建议使用永久重定向 (301)" -ForegroundColor Yellow
        $location = $response.Headers.Location
        Write-Host "✓ 重定向目标: $location" -ForegroundColor Green
    } else {
        Write-Host "✗ 未检测到重定向，状态码: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 重定向测试失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 检查IIS配置
Write-Host "第五步：检查IIS配置..." -ForegroundColor Cyan

try {
    Import-Module WebAdministration -ErrorAction Stop
    
    $siteName = "xiaoyuanst.com"
    $site = Get-Website -Name $siteName -ErrorAction SilentlyContinue
    
    if ($site) {
        Write-Host "✓ 站点存在: $siteName" -ForegroundColor Green
        Write-Host "  状态: $($site.State)" -ForegroundColor White
        
        $bindings = Get-WebBinding -Name $siteName
        Write-Host "  域名绑定:" -ForegroundColor White
        foreach ($binding in $bindings) {
            Write-Host "    - $($binding.bindingInformation)" -ForegroundColor Gray
        }
    } else {
        Write-Host "✗ 站点不存在: $siteName" -ForegroundColor Red
    }
} catch {
    Write-Host "⚠ 无法检查IIS配置: $($_.Exception.Message)" -ForegroundColor Yellow
}

# 总结
Write-Host ""
Write-Host "=== 测试总结 ===" -ForegroundColor Green

$totalTests = $localTests.Count + $proxyTests.Count + $domainTests.Count
$totalSuccess = $localSuccess + $proxySuccess + $domainSuccess

Write-Host "测试结果: $totalSuccess/$totalTests 通过" -ForegroundColor White

if ($totalSuccess -eq $totalTests) {
    Write-Host "🎉 所有测试通过！域名重定向配置成功！" -ForegroundColor Green
    Write-Host ""
    Write-Host "您现在可以使用以下地址访问网站:" -ForegroundColor Yellow
    Write-Host "  ✓ http://xiaoyuanst.com" -ForegroundColor Green
    Write-Host "  ✓ http://www.xiaoyuanst.com (自动重定向)" -ForegroundColor Green
} else {
    Write-Host "❌ 部分测试失败，请检查配置" -ForegroundColor Red
    Write-Host ""
    Write-Host "故障排除建议:" -ForegroundColor Yellow
    Write-Host "1. 确保Flask应用正在运行: python run.py" -ForegroundColor White
    Write-Host "2. 检查IIS服务状态: Get-Service W3SVC" -ForegroundColor White
    Write-Host "3. 重启IIS: iisreset" -ForegroundColor White
    Write-Host "4. 检查防火墙设置" -ForegroundColor White
    Write-Host "5. 查看IIS日志和Flask应用日志" -ForegroundColor White
}

Write-Host ""
