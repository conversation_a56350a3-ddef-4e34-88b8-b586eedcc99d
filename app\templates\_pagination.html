<!-- 通用分页宏 -->
{% macro render_pagination(pagination, endpoint=none, **kwargs) %}
{% if pagination and pagination.pages > 1 %}
<nav aria-label="分页导航" class="mt-4">
    <ul class="pagination justify-content-center">
        <li class="page-item {% if not pagination.has_prev %}disabled{% endif %}">
            <a class="page-link" href="{{ url_for(endpoint or request.endpoint, page=pagination.prev_num, **kwargs) if pagination.has_prev else '#' }}" aria-label="上一页">
                <span aria-hidden="true">&laquo;</span>
            </a>
        </li>

        {% for page_num in pagination.iter_pages(left_edge=1, right_edge=1, left_current=2, right_current=2) %}
            {% if page_num %}
                {% if page_num == pagination.page %}
                <li class="page-item active">
                    <a class="page-link" href="#">{{ page_num }}</a>
                </li>
                {% else %}
                <li class="page-item">
                    <a class="page-link" href="{{ url_for(endpoint or request.endpoint, page=page_num, **kwargs) }}">{{ page_num }}</a>
                </li>
                {% endif %}
            {% else %}
            <li class="page-item disabled">
                <a class="page-link" href="#">...</a>
            </li>
            {% endif %}
        {% endfor %}

        <li class="page-item {% if not pagination.has_next %}disabled{% endif %}">
            <a class="page-link" href="{{ url_for(endpoint or request.endpoint, page=pagination.next_num, **kwargs) if pagination.has_next else '#' }}" aria-label="下一页">
                <span aria-hidden="true">&raquo;</span>
            </a>
        </li>
    </ul>
</nav>
<div class="text-center">
    <small class="text-muted">
        显示第 {{ pagination.page }} 页，共 {{ pagination.pages }} 页，总计 {{ pagination.total }} 条记录
    </small>
</div>
{% endif %}
{% endmacro %}

<!-- 兼容旧版本的自动分页 -->
{% if pagination is defined %}
    {% set items = pagination %}
{% elif users is defined %}
    {% set items = users %}
{% elif logs is defined %}
    {% set items = logs %}
{% elif records is defined %}
    {% set items = records %}
{% endif %}

{% if items and items.pages > 1 %}
{{ render_pagination(items) }}
{% endif %}
