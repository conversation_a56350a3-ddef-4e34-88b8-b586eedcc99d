#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接调试脚本
用于诊断数据库连接问题
"""

import pyodbc
import socket
import telnetlib
from urllib.parse import quote_plus

def test_network_connectivity():
    """测试网络连通性"""
    print("=== 网络连通性测试 ===")
    
    server = "14.103.246.164"
    port = 1433  # SQL Server默认端口
    
    try:
        # 测试TCP连接
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)
        result = sock.connect_ex((server, port))
        sock.close()
        
        if result == 0:
            print(f"✓ 网络连接正常: {server}:{port}")
            return True
        else:
            print(f"✗ 网络连接失败: {server}:{port} (错误代码: {result})")
            return False
            
    except Exception as e:
        print(f"✗ 网络测试异常: {str(e)}")
        return False

def test_different_drivers():
    """测试不同的ODBC驱动"""
    print("\n=== ODBC驱动测试 ===")
    
    # 可能的驱动列表
    drivers = [
        "SQL Server",
        "ODBC Driver 17 for SQL Server",
        "ODBC Driver 18 for SQL Server",
        "SQL Server Native Client 11.0"
    ]
    
    server = "14.103.246.164"
    database = "StudentsCMSSP"
    username = "StudentsCMSSP"
    password = "Xg2LS44Cyz5Zt8"
    
    for driver in drivers:
        print(f"\n尝试驱动: {driver}")
        try:
            conn_str = f"DRIVER={{{driver}}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
            print(f"连接字符串: {conn_str}")
            
            conn = pyodbc.connect(conn_str, timeout=10)
            cursor = conn.cursor()
            cursor.execute("SELECT @@VERSION")
            version = cursor.fetchone()[0]
            print(f"✓ 连接成功! 数据库版本: {version[:50]}...")
            cursor.close()
            conn.close()
            return driver
            
        except Exception as e:
            print(f"✗ 连接失败: {str(e)}")
    
    return None

def test_connection_with_encryption():
    """测试带加密的连接"""
    print("\n=== 加密连接测试 ===")
    
    server = "14.103.246.164"
    database = "StudentsCMSSP"
    username = "StudentsCMSSP"
    password = "Xg2LS44Cyz5Zt8"
    
    # 不同的加密选项
    encryption_options = [
        "",  # 无额外选项
        ";Encrypt=yes;TrustServerCertificate=yes",
        ";Encrypt=no",
        ";Encrypt=yes;TrustServerCertificate=no",
    ]
    
    drivers = ["SQL Server", "ODBC Driver 17 for SQL Server"]
    
    for driver in drivers:
        for encrypt_option in encryption_options:
            print(f"\n尝试驱动 {driver} 加密选项: {encrypt_option}")
            try:
                conn_str = f"DRIVER={{{driver}}};SERVER={server};DATABASE={database};UID={username};PWD={password}{encrypt_option}"
                print(f"连接字符串: {conn_str}")
                
                conn = pyodbc.connect(conn_str, timeout=10)
                cursor = conn.cursor()
                cursor.execute("SELECT @@VERSION")
                version = cursor.fetchone()[0]
                print(f"✓ 连接成功! 数据库版本: {version[:50]}...")
                cursor.close()
                conn.close()
                return conn_str
                
            except Exception as e:
                print(f"✗ 连接失败: {str(e)}")
    
    return None

def test_master_database():
    """测试连接到master数据库"""
    print("\n=== Master数据库连接测试 ===")
    
    server = "14.103.246.164"
    username = "StudentsCMSSP"
    password = "Xg2LS44Cyz5Zt8"
    
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={server};DATABASE=master;UID={username};PWD={password}"
        print(f"连接字符串: {conn_str}")
        
        conn = pyodbc.connect(conn_str, timeout=10)
        cursor = conn.cursor()
        
        # 检查用户权限
        cursor.execute("SELECT SYSTEM_USER, USER_NAME()")
        user_info = cursor.fetchone()
        print(f"✓ 连接成功! 系统用户: {user_info[0]}, 数据库用户: {user_info[1]}")
        
        # 检查可访问的数据库
        cursor.execute("""
            SELECT name FROM sys.databases 
            WHERE HAS_DBACCESS(name) = 1
            ORDER BY name
        """)
        databases = cursor.fetchall()
        print("✓ 可访问的数据库:")
        for db in databases:
            print(f"  - {db[0]}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"✗ Master数据库连接失败: {str(e)}")
        return False

def test_alternative_credentials():
    """测试其他可能的凭据组合"""
    print("\n=== 替代凭据测试 ===")
    
    server = "14.103.246.164"
    database = "StudentsCMSSP"
    
    # 可能的用户名/密码组合
    credentials = [
        ("StudentsCMSSP", "Xg2LS44Cyz5Zt8"),
        ("sa", "Xg2LS44Cyz5Zt8"),
        ("StudentsCMSSP", "StudentsCMSSP"),
        ("admin", "Xg2LS44Cyz5Zt8"),
    ]
    
    for username, password in credentials:
        print(f"\n尝试用户: {username}")
        try:
            conn_str = f"DRIVER={{SQL Server}};SERVER={server};DATABASE={database};UID={username};PWD={password}"
            
            conn = pyodbc.connect(conn_str, timeout=10)
            cursor = conn.cursor()
            cursor.execute("SELECT @@VERSION")
            version = cursor.fetchone()[0]
            print(f"✓ 连接成功! 用户: {username}")
            cursor.close()
            conn.close()
            return (username, password)
            
        except Exception as e:
            print(f"✗ 用户 {username} 连接失败: {str(e)}")
    
    return None

def main():
    """主函数"""
    print("数据库连接调试开始...")
    print("=" * 60)
    
    # 1. 测试网络连通性
    network_ok = test_network_connectivity()
    
    if not network_ok:
        print("\n❌ 网络连接失败，请检查:")
        print("1. 服务器IP地址是否正确")
        print("2. 服务器是否在线")
        print("3. 防火墙是否阻止连接")
        print("4. SQL Server是否启用TCP/IP协议")
        return
    
    # 2. 测试不同驱动
    working_driver = test_different_drivers()
    
    # 3. 测试加密连接
    working_conn_str = test_connection_with_encryption()
    
    # 4. 测试master数据库
    master_ok = test_master_database()
    
    # 5. 测试替代凭据
    working_creds = test_alternative_credentials()
    
    # 输出建议
    print("\n" + "=" * 60)
    print("诊断结果和建议:")
    print("=" * 60)
    
    if working_conn_str:
        print(f"✓ 找到可用的连接字符串:")
        print(f"  {working_conn_str}")
        print("\n建议更新config.py中的连接配置")
    elif working_creds:
        username, password = working_creds
        print(f"✓ 找到可用的凭据: {username}/{password}")
        print("建议更新config.py中的用户名和密码")
    else:
        print("❌ 所有连接尝试都失败了")
        print("\n可能的解决方案:")
        print("1. 联系数据库管理员确认用户名和密码")
        print("2. 确认SQL Server启用了SQL Server身份验证")
        print("3. 确认用户有访问数据库的权限")
        print("4. 检查服务器防火墙设置")
        print("5. 确认SQL Server服务正在运行")

if __name__ == "__main__":
    main()
