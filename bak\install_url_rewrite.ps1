# PowerShell脚本：安装IIS URL Rewrite模块
# 需要以管理员身份运行

Write-Host "正在下载IIS URL Rewrite模块..." -ForegroundColor Green

# 下载URL Rewrite模块
$url = "https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi"
$output = "$env:TEMP\rewrite_amd64_en-US.msi"

try {
    Invoke-WebRequest -Uri $url -OutFile $output
    Write-Host "下载完成，正在安装..." -ForegroundColor Green
    
    # 静默安装
    Start-Process msiexec.exe -Wait -ArgumentList "/i $output /quiet"
    
    Write-Host "URL Rewrite模块安装完成！" -ForegroundColor Green
    Write-Host "请重启IIS服务以使更改生效。" -ForegroundColor Yellow
    
    # 清理下载文件
    Remove-Item $output -Force
    
} catch {
    Write-Host "下载或安装失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动下载并安装URL Rewrite模块" -ForegroundColor Yellow
}

# 重启IIS
Write-Host "正在重启IIS服务..." -ForegroundColor Green
iisreset

Write-Host "安装完成！" -ForegroundColor Green
