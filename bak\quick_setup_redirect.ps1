# 快速域名重定向配置脚本
# 简化版本，专注于核心功能

Write-Host "=== 快速配置域名重定向 ===" -ForegroundColor Green

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsIn<PERSON><PERSON>([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "需要管理员权限，请以管理员身份运行PowerShell" -ForegroundColor Red
    exit 1
}

# 导入IIS模块
Import-Module WebAdministration

# 检查站点
$siteName = "xiaoyuanst.com"
$site = Get-Website -Name $siteName -ErrorAction SilentlyContinue

if (-not $site) {
    Write-Host "站点不存在，请先运行 setup_xiaoyuanst_domain.ps1" -ForegroundColor Red
    exit 1
}

Write-Host "找到站点: $siteName" -ForegroundColor Green

# 检查域名绑定
$bindings = Get-WebBinding -Name $siteName
$hasWww = $false

foreach ($binding in $bindings) {
    if ($binding.bindingInformation -like "*www.xiaoyuanst.com*") {
        $hasWww = $true
        break
    }
}

# 添加www绑定（如果不存在）
if (-not $hasWww) {
    Write-Host "添加www域名绑定..." -ForegroundColor Yellow
    New-WebBinding -Name $siteName -IPAddress "*" -Port 80 -HostHeader "www.xiaoyuanst.com"
    Write-Host "www域名绑定添加成功" -ForegroundColor Green
} else {
    Write-Host "www域名绑定已存在" -ForegroundColor Green
}

# 备份web.config
$webConfigPath = "C:\StudentsCMSSP\web.config"
if (Test-Path $webConfigPath) {
    $backupPath = "$webConfigPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $webConfigPath $backupPath
    Write-Host "web.config已备份" -ForegroundColor Green
}

# 重启IIS
Write-Host "重启IIS..." -ForegroundColor Yellow
iisreset /noforce

Write-Host "配置完成！" -ForegroundColor Green
Write-Host "现在可以访问:" -ForegroundColor White
Write-Host "  http://xiaoyuanst.com" -ForegroundColor Cyan
Write-Host "  http://www.xiaoyuanst.com (会重定向到上面的地址)" -ForegroundColor Cyan
