# 修复 IIS URL Rewrite 错误指南

## 常见错误类型

### 1. HTTP 错误 500.50 - URL Rewrite Module Error
```
The server variable "HTTP_X_FORWARDED_FOR" is not allowed to be set.
Add the server variable name to the allowed server variable list.
```

### 2. HTTP 错误 500.52 - URL Rewrite Module Error
```
不能在此路径中使用此配置节。如果在父级别上锁定了该节，便会出现这种情况。
锁定是默认设置的(overrideModeDefault="Deny")
配置源: <allowedServerVariables>
```

## 错误原因
- **500.50错误**: IIS URL Rewrite模块默认不允许设置某些服务器变量
- **500.52错误**: `allowedServerVariables`配置节在站点级别被锁定，只能在全局级别配置

涉及的服务器变量：
- `HTTP_X_FORWARDED_FOR`
- `HTTP_X_FORWARDED_PROTO`
- `HTTP_X_FORWARDED_HOST`

## 快速修复方法

### 方法一：使用自动修复脚本（推荐）

以管理员身份运行PowerShell，执行：

```powershell
# 修复500.50错误
.\fix_url_rewrite_error.ps1

# 修复500.52错误
.\fix_url_rewrite_500_52_error.ps1
```

### 方法二：手动修复

#### 1. 检查URL Rewrite模块
```powershell
# 检查模块是否安装
Get-WebGlobalModule -Name "RewriteModule"

# 如果未安装，下载并安装
$url = "https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi"
$output = "$env:TEMP\rewrite_amd64_en-US.msi"
Invoke-WebRequest -Uri $url -OutFile $output
Start-Process msiexec.exe -Wait -ArgumentList "/i $output /quiet"
```

#### 2. 配置允许的服务器变量（全局级别）
```powershell
# 在全局级别添加允许的服务器变量（避免500.52错误）
$serverVariables = @("HTTP_X_FORWARDED_FOR", "HTTP_X_FORWARDED_PROTO", "HTTP_X_FORWARDED_HOST", "HTTP_X_REAL_IP")

foreach ($variable in $serverVariables) {
    Add-WebConfigurationProperty -PSPath "MACHINE/WEBROOT/APPHOST" -Filter "system.webServer/rewrite/allowedServerVariables" -Name "." -Value @{name=$variable}
}
```

#### 3. 更新web.config文件
确保web.config文件**不包含**站点级别的`allowedServerVariables`配置（避免500.52错误）：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- URL重写规则 - 反向代理到Flask应用 -->
        <!-- 注意：allowedServerVariables 必须在IIS全局级别配置，不能在站点级别配置 -->
        <rewrite>
            <rules>
                <rule name="ReverseProxyInboundRule1" stopProcessing="true">
                    <match url="(.*)" />
                    <action type="Rewrite" url="http://127.0.0.1:8080/{R:1}" />
                    <serverVariables>
                        <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
                        <set name="HTTP_X_FORWARDED_PROTO" value="http" />
                        <set name="HTTP_X_FORWARDED_HOST" value="{HTTP_HOST}" />
                    </serverVariables>
                </rule>
            </rules>
        </rewrite>

        <httpErrors errorMode="Detailed" />
    </system.webServer>
</configuration>
```

#### 4. 重启IIS服务
```powershell
# 重启应用程序池
Restart-WebAppPool -Name "xiaoyuanst.com_AppPool"

# 重启站点
Stop-Website -Name "xiaoyuanst.com"
Start-Website -Name "xiaoyuanst.com"

# 重启IIS
iisreset /noforce
```

## 验证修复

### 1. 检查配置
```powershell
# 运行测试脚本
.\test_xiaoyuanst_domain.ps1
```

### 2. 测试访问
- 本地访问：http://localhost
- IP访问：http://**************
- 域名访问：http://xiaoyuanst.com

### 3. 检查日志
如果仍有问题，检查IIS日志：
```
C:\inetpub\logs\LogFiles\W3SVC1\
```

## 常见问题

### Q1: 仍然出现500.50错误
**解决方案：**
1. 确认URL Rewrite模块已正确安装
2. 检查web.config语法是否正确
3. 确认Flask应用在端口8080上运行
4. 查看详细的IIS错误日志

### Q2: 403错误
**解决方案：**
1. 检查站点权限设置
2. 确认IIS_IUSRS和IUSR用户有适当权限
3. 检查防火墙设置

### Q3: 502错误
**解决方案：**
1. 确认Flask应用正在运行
2. 检查端口8080是否被占用
3. 验证代理配置是否正确

## 预防措施

1. **备份配置**：修改前备份web.config文件
2. **测试环境**：在测试环境中先验证配置
3. **监控日志**：定期检查IIS和应用程序日志
4. **文档记录**：记录所有配置更改

## 相关文件

- `web.config` - IIS配置文件
- `web.config.fixed` - 修复后的配置文件模板
- `fix_url_rewrite_error.ps1` - 自动修复脚本
- `test_xiaoyuanst_domain.ps1` - 测试脚本
- `setup_xiaoyuanst_domain.ps1` - 完整配置脚本

## 技术支持

如果问题仍然存在，请：
1. 运行完整的诊断脚本
2. 收集IIS日志和Windows事件日志
3. 检查Flask应用程序日志
4. 验证网络连接和DNS配置
