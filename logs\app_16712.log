2025-06-18 21:57:39,624 INFO: 应用启动 - PID: 16712 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-18 21:57:46,477 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 21:57:46,502 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 21:58:02,709 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 21:58:02,721 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 22:01:18,415 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:01:41,064 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:01:44,299 INFO: 库存统计页面：为区域 [44] 找到 1 个关联供应商 [in C:\StudentsCMSSP\app\routes\inventory.py:992]
2025-06-18 22:01:53,753 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:02:00,742 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-18 22:02:00,742 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-18 22:02:00,743 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-18 22:02:00,743 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-18 22:02:12,114 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-18', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-18 22:02:12,116 INFO: 查询日期: 2025-06-18, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-18 22:02:12,117 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-18 22:02:12,117 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-18 22:02:12,122 INFO: 未找到周菜单 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:419]
2025-06-18 22:02:24,949 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-18 22:02:24,949 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-18 22:02:24,952 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-18 22:02:24,952 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-18 22:02:39,708 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:03:03,306 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:04:08,554 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 22:04:08,557 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 22:04:26,127 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 22:04:26,134 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 22:07:29,045 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 22:07:29,055 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 22:07:54,038 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 22:07:54,045 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 22:08:17,028 INFO: 获取副表数据用于补全主表: weekly_menu_id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-18 22:08:17,030 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-18 22:08:17,031 INFO: 主表数据补全完成，准备保存: 总菜品数=0, 已补全=0, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-18 22:08:17,035 INFO: 删除现有菜单食谱(主表): weekly_menu_id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-18 22:08:17,039 INFO: 删除现有菜单食谱(副表): weekly_menu_id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-18 22:08:17,039 INFO: 保存周菜单成功(主表和副表): id=43 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-18 22:08:17,039 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-18 22:14:29,563 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 22:14:29,571 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
