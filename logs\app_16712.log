2025-06-18 21:57:39,624 INFO: 应用启动 - PID: 16712 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-18 21:57:46,477 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 21:57:46,502 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 21:58:02,709 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 21:58:02,721 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 22:01:18,415 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:01:41,064 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:01:44,299 INFO: 库存统计页面：为区域 [44] 找到 1 个关联供应商 [in C:\StudentsCMSSP\app\routes\inventory.py:992]
2025-06-18 22:01:53,753 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:02:00,742 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-18 22:02:00,742 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-18 22:02:00,743 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-18 22:02:00,743 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-18 22:02:12,114 INFO: 收到食谱分析请求: {'area_id': 44, 'consumption_date': '2025-06-18', 'meal_types': ['早餐']} [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:322]
2025-06-18 22:02:12,116 INFO: 查询日期: 2025-06-18, 区域: 44, 餐次: ['早餐'] [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:340]
2025-06-18 22:02:12,117 INFO: 查询餐次: 早餐 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:345]
2025-06-18 22:02:12,117 INFO: 日菜单功能已移除，从周菜单获取食谱 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:348]
2025-06-18 22:02:12,122 INFO: 未找到周菜单 [in C:\StudentsCMSSP\app\routes\consumption_plan_super.py:419]
2025-06-18 22:02:24,949 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-18 22:02:24,949 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-18 22:02:24,952 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-18 22:02:24,952 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-18 22:02:39,708 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-18 22:03:03,306 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
