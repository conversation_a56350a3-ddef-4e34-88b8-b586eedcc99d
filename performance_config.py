#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能优化配置
用于提高应用运行速度和响应性能
"""

import os
from datetime import timedelta
from urllib.parse import quote_plus

basedir = os.path.abspath(os.path.dirname(__file__))

class PerformanceConfig:
    """高性能配置类"""
    
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'
    
    # 关闭调试和测试模式
    DEBUG = False
    TESTING = False
    
    # CSRF配置 - 简化配置
    WTF_CSRF_ENABLED = False
    WTF_CSRF_TIME_LIMIT = 3600
    WTF_CSRF_SSL_STRICT = False
    WTF_CSRF_METHODS = ['PUT', 'PATCH', 'DELETE']
    WTF_CSRF_HEADERS = ['X-CSRFToken', 'X-CSRF-Token']
    
    # 数据库配置 - 高性能设置
    conn_str = "DRIVER={SQL Server};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;UID=StudentsCMSSP;PWD=***************"
    quoted_conn_str = quote_plus(conn_str)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"
    
    # 关闭SQLAlchemy跟踪修改
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 高性能数据库连接池配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_recycle': 7200,        # 连接回收时间2小时
        'pool_timeout': 60,          # 连接超时时间1分钟
        'pool_size': 30,             # 连接池大小增加
        'max_overflow': 20,          # 最大溢出连接数
        'pool_pre_ping': True,       # 连接前ping
        'echo': False,               # 关闭SQL日志
        'echo_pool': False,          # 关闭连接池日志
        'connect_args': {
            'timeout': 30,           # 连接超时
            'autocommit': False,     # 关闭自动提交
        }
    }
    
    # 上传文件配置
    UPLOAD_FOLDER = os.path.join(basedir, 'app/static/uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    
    # 会话配置 - 延长会话时间减少重新登录
    PERMANENT_SESSION_LIFETIME = timedelta(days=30)
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SECURE = False  # 本地开发环境
    
    # 管理员配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL') or '<EMAIL>'
    
    # 分页配置 - 增加每页显示数量
    ITEMS_PER_PAGE = 20
    
    # API配置
    API_KEY = os.environ.get('API_KEY') or 'system_fix_api_key_2025'
    
    # Redis配置 - 如果可用的话
    REDIS_HOST = os.environ.get('REDIS_HOST') or 'localhost'
    REDIS_PORT = int(os.environ.get('REDIS_PORT') or 6379)
    REDIS_DB = int(os.environ.get('REDIS_DB') or 0)
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD') or None
    
    # 日志配置 - 减少日志输出
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'WARNING'  # 只记录警告和错误
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'app.log')
    
    # 缓存配置 - 启用缓存提高性能
    CACHE_TYPE = 'simple'  # 使用简单内存缓存
    CACHE_DEFAULT_TIMEOUT = 600  # 10分钟缓存
    
    # 性能优化配置
    SEND_FILE_MAX_AGE_DEFAULT = timedelta(hours=12)  # 静态文件缓存12小时
    
    # 模板缓存
    TEMPLATES_AUTO_RELOAD = False  # 关闭模板自动重载
    
    # JSON配置
    JSON_SORT_KEYS = False  # 关闭JSON键排序
    JSONIFY_PRETTYPRINT_REGULAR = False  # 关闭JSON美化
    
    @staticmethod
    def init_app(app):
        # 确保上传目录存在
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        # 确保日志目录存在
        os.makedirs(os.path.dirname(app.config['LOG_FILE']), exist_ok=True)
        
        # 配置日志级别
        import logging
        logging.getLogger('sqlalchemy.engine').setLevel(logging.WARNING)
        logging.getLogger('sqlalchemy.pool').setLevel(logging.WARNING)
        logging.getLogger('werkzeug').setLevel(logging.WARNING)

class DevelopmentPerformanceConfig(PerformanceConfig):
    """开发环境高性能配置"""
    pass

class ProductionPerformanceConfig(PerformanceConfig):
    """生产环境高性能配置"""
    
    # 生产环境应该使用环境变量设置敏感信息
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD')
    
    # 生产环境安全配置
    SESSION_COOKIE_SECURE = True
    SESSION_COOKIE_HTTPONLY = True
    
    # 更严格的日志级别
    LOG_LEVEL = 'ERROR'

# 配置字典
performance_config = {
    'development': DevelopmentPerformanceConfig,
    'production': ProductionPerformanceConfig,
    'default': DevelopmentPerformanceConfig
}
