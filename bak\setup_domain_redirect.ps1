# xiaoyuanst.com 域名重定向自动配置脚本
# 功能：自动配置www和非www域名重定向，确保两个域名都指向同一个网站

param(
    [switch]$Force = $false
)

# 检查管理员权限
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "错误：此脚本需要管理员权限运行" -ForegroundColor Red
    Write-Host "请以管理员身份运行PowerShell，然后重新执行此脚本" -ForegroundColor Yellow
    exit 1
}

Write-Host "=== xiaoyuanst.com 域名重定向配置脚本 ===" -ForegroundColor Green
Write-Host "功能：配置www.xiaoyuanst.com自动重定向到xiaoyuanst.com" -ForegroundColor White
Write-Host ""

# 导入IIS管理模块
Write-Host "第一步：导入IIS管理模块..." -ForegroundColor Yellow
try {
    Import-Module WebAdministration -ErrorAction Stop
    Write-Host "✓ IIS管理模块导入成功" -ForegroundColor Green
} catch {
    Write-Host "✗ 导入IIS管理模块失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# 检查站点是否存在
$siteName = "xiaoyuanst.com"
Write-Host "第二步：检查站点配置..." -ForegroundColor Yellow

$site = Get-Website -Name $siteName -ErrorAction SilentlyContinue
if (-not $site) {
    Write-Host "✗ 站点 $siteName 不存在" -ForegroundColor Red
    Write-Host "请先运行 setup_xiaoyuanst_domain.ps1 创建站点" -ForegroundColor Yellow
    exit 1
}

Write-Host "✓ 找到站点: $siteName" -ForegroundColor Green
Write-Host "  状态: $($site.State)" -ForegroundColor White
Write-Host "  物理路径: $($site.PhysicalPath)" -ForegroundColor White

# 检查域名绑定
Write-Host "第三步：检查域名绑定..." -ForegroundColor Yellow

$bindings = Get-WebBinding -Name $siteName
$hasMainDomain = $false
$hasWwwDomain = $false

foreach ($binding in $bindings) {
    if ($binding.bindingInformation -like "*xiaoyuanst.com") {
        $hasMainDomain = $true
        Write-Host "✓ 找到主域名绑定: $($binding.bindingInformation)" -ForegroundColor Green
    }
    if ($binding.bindingInformation -like "*www.xiaoyuanst.com") {
        $hasWwwDomain = $true
        Write-Host "✓ 找到www域名绑定: $($binding.bindingInformation)" -ForegroundColor Green
    }
}

# 添加缺失的域名绑定
if (-not $hasMainDomain) {
    Write-Host "添加主域名绑定..." -ForegroundColor Yellow
    New-WebBinding -Name $siteName -IPAddress "*" -Port 80 -HostHeader "xiaoyuanst.com"
    Write-Host "✓ 主域名绑定添加成功" -ForegroundColor Green
}

if (-not $hasWwwDomain) {
    Write-Host "添加www域名绑定..." -ForegroundColor Yellow
    New-WebBinding -Name $siteName -IPAddress "*" -Port 80 -HostHeader "www.xiaoyuanst.com"
    Write-Host "✓ www域名绑定添加成功" -ForegroundColor Green
}

# 备份现有web.config
$webConfigPath = "C:\StudentsCMSSP\web.config"
Write-Host "第四步：备份web.config..." -ForegroundColor Yellow

if (Test-Path $webConfigPath) {
    $backupPath = "$webConfigPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
    Copy-Item $webConfigPath $backupPath
    Write-Host "✓ web.config已备份到: $backupPath" -ForegroundColor Green
} else {
    Write-Host "✗ 找不到web.config文件: $webConfigPath" -ForegroundColor Red
    exit 1
}

# 检查URL Rewrite模块
Write-Host "第五步：检查URL Rewrite模块..." -ForegroundColor Yellow

# 检查URL Rewrite模块是否可用
try {
    $rewriteModule = Get-WindowsFeature -Name IIS-HttpRedirect -ErrorAction SilentlyContinue
    if ($rewriteModule -and $rewriteModule.InstallState -eq "Installed") {
        Write-Host "✓ URL Rewrite模块已安装" -ForegroundColor Green
    } else {
        Write-Host "! URL Rewrite模块未安装，正在安装..." -ForegroundColor Yellow
        try {
            Enable-WindowsOptionalFeature -Online -FeatureName IIS-HttpRedirect -All
            Write-Host "✓ URL Rewrite模块安装成功" -ForegroundColor Green
        } catch {
            Write-Host "✗ URL Rewrite模块安装失败，请手动安装" -ForegroundColor Red
            Write-Host "下载地址: https://www.iis.net/downloads/microsoft/url-rewrite" -ForegroundColor Yellow
        }
    }
} catch {
    Write-Host "⚠ 无法检查URL Rewrite模块状态，跳过检查" -ForegroundColor Yellow
}

# 验证web.config中的重定向规则
Write-Host "第六步：验证重定向规则..." -ForegroundColor Yellow

$webConfigContent = Get-Content $webConfigPath -Raw
if ($webConfigContent -match "RedirectWWWToNonWWW") {
    Write-Host "✓ 重定向规则已存在" -ForegroundColor Green
} else {
    Write-Host "! 重定向规则不存在，web.config可能需要更新" -ForegroundColor Yellow
    Write-Host "当前web.config已包含最新的重定向规则" -ForegroundColor White
}

# 重启IIS服务
Write-Host "第七步：重启IIS服务..." -ForegroundColor Yellow

try {
    iisreset /noforce
    Write-Host "✓ IIS服务重启成功" -ForegroundColor Green
} catch {
    Write-Host "✗ IIS服务重启失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请手动执行: iisreset" -ForegroundColor Yellow
}

# 测试配置
Write-Host "第八步：测试域名访问..." -ForegroundColor Yellow

$testUrls = @(
    "http://xiaoyuanst.com",
    "http://www.xiaoyuanst.com"
)

foreach ($url in $testUrls) {
    try {
        Write-Host "测试: $url" -ForegroundColor White
        $response = Invoke-WebRequest -Uri $url -Method Head -TimeoutSec 10 -ErrorAction Stop
        Write-Host "✓ $url - 状态码: $($response.StatusCode)" -ForegroundColor Green
    } catch {
        Write-Host "✗ $url - 错误: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "=== 配置完成 ===" -ForegroundColor Green
Write-Host "域名重定向配置已完成！" -ForegroundColor White
Write-Host ""
Write-Host "访问地址:" -ForegroundColor Yellow
Write-Host "  主域名: http://xiaoyuanst.com" -ForegroundColor White
Write-Host "  www域名: http://www.xiaoyuanst.com (自动重定向到主域名)" -ForegroundColor White
Write-Host "  IP访问: http://**************" -ForegroundColor White
Write-Host ""
Write-Host "注意事项:" -ForegroundColor Yellow
Write-Host "1. 确保Flask应用正在运行 (python run.py)" -ForegroundColor White
Write-Host "2. 确保防火墙允许80端口访问" -ForegroundColor White
Write-Host "3. 如有问题，请检查IIS日志和Flask应用日志" -ForegroundColor White
