2025-06-18 20:26:45,333 INFO: 应用启动 - PID: 32764 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-18 20:27:06,228 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 20:27:06,349 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 20:27:17,610 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 20:27:17,719 ERROR: 处理陪餐记录 7 时出错: 'str' object has no attribute 'strftime' [in C:\StudentsCMSSP\app\routes\dashboard_api.py:88]
2025-06-18 20:27:17,721 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-18 20:27:31,528 INFO: 查询菜谱：日期=2025-06-18, 星期=2(0=周一), day_of_week=3, 餐次=午餐, 区域ID=22 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-18 20:27:31,595 INFO: 找到 0 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-18 20:27:31,595 INFO: 未找到 2025-06-18 午餐 的菜谱信息 [in C:\StudentsCMSSP\app\routes\food_trace.py:365]
2025-06-18 20:27:31,658 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-18 20:29:42,366 INFO: 获取副表数据用于补全主表: weekly_menu_id=40 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-18 20:29:42,472 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-18 20:29:42,473 INFO: 主表数据补全完成，准备保存: 总菜品数=0, 已补全=0, 未补全=0 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-18 20:29:42,525 INFO: 删除现有菜单食谱(主表): weekly_menu_id=40 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-18 20:29:42,580 INFO: 删除现有菜单食谱(副表): weekly_menu_id=40 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-18 20:29:42,608 INFO: 保存周菜单成功(主表和副表): id=40 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-18 20:29:42,609 INFO: 菜单缓存已清理 [in C:\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-18 20:32:01,543 INFO: 开始查询最近 5 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-18 20:32:01,665 INFO: 成功获取 0 条陪餐记录 [in C:\StudentsCMSSP\app\routes\dashboard_api.py:91]
