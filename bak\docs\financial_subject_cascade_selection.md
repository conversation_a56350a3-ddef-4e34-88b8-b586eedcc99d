# 财务凭证会计科目分级联动选择功能说明

## 功能概述

为了提高财务凭证录入的准确性和效率，我们实现了会计科目的分级联动选择功能。该功能将会计科目的选择分为三个步骤，用户可以逐级选择，避免在大量科目中查找的困扰。

## 功能特点

### 1. 三级联动选择
- **第一步：科目类型** - 选择会计科目的大类（资产、负债、净资产、收入、费用）
- **第二步：一级科目** - 根据科目类型显示对应的一级科目
- **第三步：明细科目** - 根据一级科目显示对应的明细科目

### 2. 智能交互
- 步骤指示器显示当前选择进度
- 自动启用/禁用下级选择框
- 选择完成后显示完成状态

### 3. 编辑支持
- 编辑明细时自动回显科目选择
- 支持修改已选择的科目

## 使用方法

### 添加新明细
1. 点击"添加明细"按钮
2. 在弹出的模态框中：
   - 第一步：从下拉框中选择科目类型（如：资产类科目）
   - 第二步：选择具体的一级科目（如：1001 - 库存现金）
   - 第三步：选择最终的明细科目（如果有下级科目）
3. 填写其他必要信息（摘要、金额等）
4. 保存明细

### 编辑现有明细
1. 点击明细行的"编辑"按钮
2. 系统自动回显当前科目的选择路径
3. 可以重新选择科目或修改其他信息
4. 保存修改

## 技术实现

### 前端实现
- 使用jQuery实现三级联动逻辑
- AJAX异步加载科目数据
- 动态更新步骤指示器状态

### 后端API
- `/financial/accounting-subjects/types` - 获取科目类型列表
- `/financial/accounting-subjects/by-type` - 根据类型获取一级科目
- `/financial/accounting-subjects/by-parent` - 根据上级科目获取下级科目

### 数据结构
```json
{
  "id": 1,
  "code": "1001",
  "name": "库存现金",
  "display_name": "1001 - 库存现金",
  "subject_type": "资产",
  "level": 1,
  "parent_id": null
}
```

## 优势

1. **提高效率** - 分级选择比在长列表中查找更快
2. **减少错误** - 逐级选择避免选错科目
3. **用户友好** - 清晰的步骤指示和视觉反馈
4. **灵活性** - 支持不同层级的科目结构

## 注意事项

1. 如果一级科目没有下级科目，系统会自动将其作为最终科目
2. 编辑时需要等待科目数据加载完成后才能看到正确的选择状态
3. 科目数据来源于系统预设科目和学校自定义科目

## 后续优化

1. 添加科目搜索功能
2. 支持常用科目快速选择
3. 添加科目使用频率统计
4. 优化移动端显示效果
