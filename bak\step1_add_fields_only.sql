-- 第一步：仅添加字段到 stock_ins 表
-- 请在 SQL Server Management Studio 中执行此脚本

USE [StudentsCMSSP]
GO

PRINT '开始添加字段到 stock_ins 表...'

-- 添加 area_id 字段
ALTER TABLE stock_ins ADD area_id INT NULL;
PRINT '✓ 添加 area_id 字段'

-- 添加 supplier_id 字段
ALTER TABLE stock_ins ADD supplier_id INT NULL;
PRINT '✓ 添加 supplier_id 字段'

-- 添加 payable_id 字段
ALTER TABLE stock_ins ADD payable_id INT NULL;
PRINT '✓ 添加 payable_id 字段'

-- 添加 voucher_id 字段
ALTER TABLE stock_ins ADD voucher_id INT NULL;
PRINT '✓ 添加 voucher_id 字段'

-- 添加 is_financial_confirmed 字段
ALTER TABLE stock_ins ADD is_financial_confirmed BIT NOT NULL DEFAULT 0;
PRINT '✓ 添加 is_financial_confirmed 字段'

-- 添加 total_cost 字段
ALTER TABLE stock_ins ADD total_cost DECIMAL(10,2) NULL;
PRINT '✓ 添加 total_cost 字段'

-- 添加 financial_confirmed_at 字段
ALTER TABLE stock_ins ADD financial_confirmed_at DATETIME2(1) NULL;
PRINT '✓ 添加 financial_confirmed_at 字段'

-- 添加 financial_confirmed_by 字段
ALTER TABLE stock_ins ADD financial_confirmed_by INT NULL;
PRINT '✓ 添加 financial_confirmed_by 字段'

PRINT '所有字段添加完成！'
PRINT '请继续执行 step2_update_data.sql'

GO
