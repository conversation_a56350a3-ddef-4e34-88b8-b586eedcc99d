"""
日期时间处理工具模块

提供统一的日期时间处理功能，确保与数据库DATETIME2(1)类型兼容。
"""

from datetime import datetime, date, time, timedelta
from flask import current_app

def get_current_time():
    """
    获取当前时间，去除微秒部分

    Returns:
        datetime: 当前时间，精确到0.1秒
    """
    return datetime.now().replace(microsecond=0)

def format_datetime(dt_value, format_str='%Y-%m-%d %H:%M:%S'):
    """
    安全地格式化日期时间值，处理各种可能的输入类型

    参数:
        dt_value: 要格式化的日期时间值，可以是datetime对象、字符串或None
        format_str: 格式化字符串，默认为'%Y-%m-%d %H:%M:%S'

    返回:
        格式化后的字符串，如果输入为None则返回None
    """
    if dt_value is None:
        return None

    # 如果已经是字符串，直接返回
    if isinstance(dt_value, str):
        return dt_value

    # 如果是datetime对象，格式化它
    if hasattr(dt_value, 'strftime'):
        return dt_value.strftime(format_str)

    # 其他情况，尝试转换为字符串
    return str(dt_value)

def format_date(date_value, format_str='%Y-%m-%d'):
    """
    安全地格式化日期值，处理各种可能的输入类型

    参数:
        date_value: 要格式化的日期值，可以是date对象、datetime对象、字符串或None
        format_str: 格式化字符串，默认为'%Y-%m-%d'

    返回:
        格式化后的字符串，如果输入为None则返回None
    """
    return format_datetime(date_value, format_str)

def parse_datetime(date_str, format_str='%Y-%m-%d'):
    """
    将字符串解析为日期时间对象

    参数:
        date_str: 要解析的日期时间字符串
        format_str: 格式化字符串，默认为'%Y-%m-%d'

    返回:
        datetime对象，如果解析失败则返回None
    """
    if date_str is None:
        return None

    # 如果已经是日期时间对象，直接返回
    if hasattr(date_str, 'strftime'):
        return date_str

    # 如果是字符串，尝试解析
    if isinstance(date_str, str):
        try:
            return datetime.strptime(date_str, format_str)
        except ValueError:
            # 尝试其他常见格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d %H:%M',
                '%Y/%m/%d'
            ]

            for fmt in formats:
                try:
                    return datetime.strptime(date_str, fmt)
                except ValueError:
                    continue

            # 所有格式都失败了
            return None

    # 其他情况
    return None

def safe_datetime(dt_value):
    """
    尝试将输入值转换为datetime对象，并去除微秒部分

    参数:
        dt_value: 要转换的值，可以是字符串、datetime对象或None

    返回:
        datetime对象，精确到0.1秒，如果转换失败则返回None
    """
    if dt_value is None:
        return None

    # 如果已经是datetime对象，去除微秒部分后返回
    if isinstance(dt_value, datetime):
        return dt_value.replace(microsecond=0)

    # 如果是字符串，尝试解析
    if isinstance(dt_value, str):
        try:
            # 尝试多种常见格式
            formats = [
                '%Y-%m-%d %H:%M:%S',
                '%Y-%m-%d %H:%M',
                '%Y-%m-%d',
                '%Y/%m/%d %H:%M:%S',
                '%Y/%m/%d %H:%M',
                '%Y/%m/%d'
            ]

            for fmt in formats:
                try:
                    dt = datetime.strptime(dt_value, fmt)
                    return dt.replace(microsecond=0)
                except ValueError:
                    continue

            # 所有格式都失败了
            return None
        except Exception:
            return None

    # 其他情况
    return None

def parse_date(date_str, format_str='%Y-%m-%d'):
    """
    解析日期字符串

    Args:
        date_str: 日期字符串
        format_str: 格式化字符串

    Returns:
        date: 解析后的日期对象
    """
    try:
        return datetime.strptime(date_str, format_str).date()
    except ValueError:
        current_app.logger.warning(f"日期解析失败: {date_str}, 格式: {format_str}")
        return None

def get_date_range(start_date, end_date):
    """
    获取日期范围

    Args:
        start_date: 开始日期
        end_date: 结束日期

    Returns:
        list: 日期范围列表
    """
    if isinstance(start_date, str):
        start_date = parse_date(start_date)

    if isinstance(end_date, str):
        end_date = parse_date(end_date)

    if not start_date or not end_date:
        return []

    date_range = []
    current_date = start_date

    while current_date <= end_date:
        date_range.append(current_date)
        current_date += timedelta(days=1)

    return date_range

def sql_datetime_format(dt):
    """
    将日期时间对象格式化为SQL Server兼容的字符串

    Args:
        dt: 日期时间对象

    Returns:
        str: 格式化后的日期时间字符串
    """
    if not dt:
        return None

    if isinstance(dt, datetime):
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    elif isinstance(dt, date):
        return dt.strftime('%Y-%m-%d')
    else:
        return str(dt)

def ensure_datetime(dt_value):
    """
    确保输入值是datetime对象，如果不是则尝试转换

    Args:
        dt_value: 输入值，可以是datetime对象、date对象、字符串或None

    Returns:
        datetime: 转换后的datetime对象，如果转换失败则返回None
    """
    from datetime import datetime, date
    import re
    from flask import current_app

    if dt_value is None:
        return None

    # 如果已经是datetime对象，直接返回
    if isinstance(dt_value, datetime):
        return dt_value.replace(microsecond=0)

    # 如果是date对象，转换为datetime
    if isinstance(dt_value, date):
        return datetime.combine(dt_value, datetime.min.time()).replace(microsecond=0)

    # 如果是字符串，尝试解析
    if isinstance(dt_value, str):
        # 清理字符串，移除多余空格
        dt_str = dt_value.strip()

        # 如果是空字符串，直接返回None
        if not dt_str:
            return None

        # 尝试多种常见格式
        formats = [
            '%Y-%m-%d %H:%M:%S',  # 2023-01-01 12:30:45
            '%Y-%m-%d %H:%M',     # 2023-01-01 12:30
            '%Y-%m-%d',           # 2023-01-01
            '%Y/%m/%d %H:%M:%S',  # 2023/01/01 12:30:45
            '%Y/%m/%d %H:%M',     # 2023/01/01 12:30
            '%Y/%m/%d',           # 2023/01/01
            '%d/%m/%Y %H:%M:%S',  # 01/01/2023 12:30:45
            '%d/%m/%Y %H:%M',     # 01/01/2023 12:30
            '%d/%m/%Y',           # 01/01/2023
            '%m/%d/%Y %H:%M:%S',  # 01/01/2023 12:30:45
            '%m/%d/%Y %H:%M',     # 01/01/2023 12:30
            '%m/%d/%Y',           # 01/01/2023
            '%d-%m-%Y %H:%M:%S',  # 01-01-2023 12:30:45
            '%d-%m-%Y %H:%M',     # 01-01-2023 12:30
            '%d-%m-%Y',           # 01-01-2023
            '%m-%d-%Y %H:%M:%S',  # 01-01-2023 12:30:45
            '%m-%d-%Y %H:%M',     # 01-01-2023 12:30
            '%m-%d-%Y',           # 01-01-2023
        ]

        for fmt in formats:
            try:
                return datetime.strptime(dt_str, fmt).replace(microsecond=0)
            except ValueError:
                continue

        # 如果标准格式都失败了，尝试使用正则表达式匹配常见模式
        try:
            # 匹配 YYYY-MM-DD 或 YYYY/MM/DD
            date_pattern = r'(\d{4})[-/](\d{1,2})[-/](\d{1,2})'
            # 匹配 HH:MM:SS 或 HH:MM
            time_pattern = r'(\d{1,2}):(\d{1,2})(?::(\d{1,2}))?'

            date_match = re.search(date_pattern, dt_str)
            time_match = re.search(time_pattern, dt_str)

            if date_match:
                year = int(date_match.group(1))
                month = int(date_match.group(2))
                day = int(date_match.group(3))

                if time_match:
                    hour = int(time_match.group(1))
                    minute = int(time_match.group(2))
                    second = int(time_match.group(3)) if time_match.group(3) else 0
                    return datetime(year, month, day, hour, minute, second)
                else:
                    return datetime(year, month, day)
        except Exception:
            pass

        # 所有尝试都失败了
        current_app.logger.warning(f"无法解析日期时间字符串: {dt_str}")
        return None

    # 其他类型，尝试转换为字符串再解析
    try:
        return ensure_datetime(str(dt_value))
    except Exception:
        current_app.logger.warning(f"无法将类型 {type(dt_value)} 转换为datetime: {dt_value}")
        return None

def safe_strftime(dt_value, format_str='%Y-%m-%d %H:%M:%S'):
    """
    安全地调用strftime方法，处理各种类型的输入

    Args:
        dt_value: 输入值，可以是datetime对象、date对象、字符串或None
        format_str: 格式化字符串

    Returns:
        str: 格式化后的字符串，如果输入无效则返回空字符串
    """
    dt = ensure_datetime(dt_value)
    if dt is None:
        return ''
    return dt.strftime(format_str)

def get_prev_next_logs(log):
    """
    获取前一天和后一天的日志

    Args:
        log: 当前日志对象

    Returns:
        tuple: (前一天日志, 后一天日志)
    """
    from app import db
    from app.models_daily_management import DailyLog
    from sqlalchemy import text

    # 如果日志对象为空，返回空结果
    if not log:
        return None, None

    # 安全地获取日期字符串
    log_date_str = safe_strftime(log.log_date, '%Y-%m-%d')
    area_id = log.area_id

    # 使用原始SQL查询，避免日期类型问题
    prev_log_sql = text("""
        SELECT TOP 1 id FROM daily_logs
        WHERE CONVERT(VARCHAR(10), log_date, 120) < :log_date
        AND area_id = :area_id
        ORDER BY log_date DESC
    """)
    prev_log_result = db.session.execute(prev_log_sql, {'log_date': log_date_str, 'area_id': area_id}).fetchone()
    prev_log = DailyLog.query.get(prev_log_result[0]) if prev_log_result else None

    next_log_sql = text("""
        SELECT TOP 1 id FROM daily_logs
        WHERE CONVERT(VARCHAR(10), log_date, 120) > :log_date
        AND area_id = :area_id
        ORDER BY log_date ASC
    """)
    next_log_result = db.session.execute(next_log_sql, {'log_date': log_date_str, 'area_id': area_id}).fetchone()
    next_log = DailyLog.query.get(next_log_result[0]) if next_log_result else None

    return prev_log, next_log
