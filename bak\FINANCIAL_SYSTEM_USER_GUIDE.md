# 学校食堂财务管理系统使用说明

## 📋 系统概述

学校食堂财务管理系统是一个完整的财务管理解决方案，从供应商管理、食材采购、入库确认到财务核算、报表分析的全流程管理。

## 🏗️ 业务流程图

```
供应商管理 → 食材采购 → 采购订单 → 货物入库 → 财务确认 → 应付账款 → 付款记录 → 财务报表
     ↓           ↓           ↓           ↓           ↓           ↓           ↓           ↓
  供应商档案   食材目录   订单管理   入库单据   财务审核   账款跟踪   付款凭证   经营分析
```

## 🎯 角色权限说明

### 1. 财务主管
- **权限范围**：所有财务功能的完整权限
- **主要职责**：财务制度制定、重要凭证审核、财务报表分析
- **操作模块**：全部财务模块

### 2. 会计人员
- **权限范围**：日常会计核算权限
- **主要职责**：凭证录入、账务处理、报表编制
- **操作模块**：会计科目、财务凭证、应付账款、财务报表

### 3. 出纳人员
- **权限范围**：现金和银行业务权限
- **主要职责**：现金管理、银行业务、付款执行
- **操作模块**：付款记录、现金凭证

### 4. 采购人员
- **权限范围**：采购相关财务权限
- **主要职责**：采购订单、入库确认、应付账款核对
- **操作模块**：应付账款、待处理入库

### 5. 管理层
- **权限范围**：财务报表查看权限
- **主要职责**：经营决策、成本控制、绩效分析
- **操作模块**：财务概览、财务报表

## 📚 详细操作指南

### 第一部分：基础设置

#### 1.1 供应商管理
**路径**：主菜单 → 采购管理 → 供应商管理

**操作步骤**：
1. 点击"新增供应商"
2. 填写供应商基本信息：
   - 供应商名称：如"绿源蔬菜批发市场"
   - 联系人：张经理
   - 联系电话：138****8888
   - 地址：市场路123号
   - 营业执照号：91****
   - 开户银行：工商银行
   - 银行账号：6222****
   - 供应商类型：蔬菜类/肉类/粮油类
3. 设置信用等级和付款条件
4. 保存供应商信息

**注意事项**：
- 供应商信息要真实完整
- 银行信息用于后续付款
- 定期更新供应商资质

#### 1.2 食材管理
**路径**：主菜单 → 库存管理 → 食材管理

**操作步骤**：
1. 点击"新增食材"
2. 填写食材信息：
   - 食材名称：如"土豆"
   - 食材编码：TD001
   - 分类：蔬菜类
   - 单位：公斤
   - 规格：中等
   - 保质期：7天
   - 存储条件：常温
3. 设置采购信息：
   - 主要供应商：绿源蔬菜批发市场
   - 采购单价：3.50元/公斤
   - 最小采购量：50公斤
4. 保存食材信息

#### 1.3 会计科目设置
**路径**：财务管理 → 会计科目

**系统预设科目**：
- **1402 原材料**
  - 140201 蔬菜类
  - 140202 肉类
  - 140203 粮油类
  - 140204 调料类
- **2201 应付账款**
  - 220101 食材供应商
  - 220102 设备供应商
- **6001 主营业务收入**
  - 600101 学生餐费收入
  - 600102 教师餐费收入
- **6401 主营业务成本**
  - 640101 食材成本
  - 640102 人工成本

**自定义科目添加**：
1. 点击"新增科目"
2. 输入科目编码：如"140205"
3. 输入科目名称：如"水果类"
4. 选择上级科目：原材料
5. 选择科目类型：资产
6. 选择余额方向：借方
7. 保存科目

### 第二部分：采购到入库流程

#### 2.1 创建采购订单
**路径**：采购管理 → 采购订单管理

**操作步骤**：
1. 点击"新建采购订单"
2. 选择供应商：绿源蔬菜批发市场
3. 添加采购明细：
   - 食材：土豆
   - 数量：100公斤
   - 单价：3.50元
   - 金额：350.00元
4. 填写交货信息：
   - 交货日期：明天
   - 交货地点：学校食堂
5. 提交审核
6. 审核通过后发送给供应商

#### 2.2 货物入库确认
**路径**：库存管理 → 入库管理

**操作步骤**：
1. 供应商送货到达
2. 库管员验收货物：
   - 检查数量：实收98公斤
   - 检查质量：合格
   - 检查包装：完好
3. 创建入库单：
   - 关联采购订单
   - 录入实际数量
   - 填写验收意见
4. 保存入库单
5. 通知财务确认

#### 2.3 财务确认入库
**路径**：财务管理 → 待处理入库

**操作步骤**：
1. 财务人员查看待确认入库单
2. 核对采购订单和入库单：
   - 供应商信息
   - 货物明细
   - 数量金额
3. 确认无误后点击"财务确认"
4. 系统自动生成应付账款

### 第三部分：财务核算流程

#### 3.1 应付账款管理
**路径**：财务管理 → 应付账款

**查看应付账款**：
- 应付账款编号：AP20241201001
- 供应商：绿源蔬菜批发市场
- 原始金额：343.00元（98公斤×3.50元）
- 余额：343.00元
- 状态：未付款
- 账龄：3天

**生成应付账款凭证**：
系统自动生成：
```
借：原材料-蔬菜类    343.00
    贷：应付账款-食材供应商    343.00
```

#### 3.2 付款记录
**路径**：财务管理 → 付款记录

**创建付款记录**：
1. 点击"新建付款"
2. 选择应付账款：AP20241201001
3. 填写付款信息：
   - 付款日期：今天
   - 付款金额：343.00元
   - 付款方式：银行转账
   - 银行账户：基本户
   - 参考号：转账凭证号
4. 保存付款记录

**生成付款凭证**：
系统自动生成：
```
借：应付账款-食材供应商    343.00
    贷：银行存款-基本户    343.00
```

#### 3.3 财务凭证管理
**路径**：财务管理 → 财务凭证

**手工凭证录入**：
1. 点击"新建凭证"
2. 选择凭证类型：收款凭证
3. 填写凭证信息：
   - 凭证日期：今天
   - 摘要：收到学生餐费
4. 录入凭证明细：
   ```
   借：银行存款-基本户    5000.00
       贷：主营业务收入-学生餐费收入    5000.00
   ```
5. 检查借贷平衡
6. 提交审核

**凭证审核流程**：
1. 审核人员查看待审核凭证
2. 检查凭证要素：
   - 日期是否正确
   - 科目是否合适
   - 金额是否准确
   - 摘要是否清楚
3. 审核通过或退回修改
4. 审核通过后自动记账

### 第四部分：成本核算

#### 4.1 食材成本核算
**月末成本结转**：
1. 统计本月食材消耗：
   - 蔬菜类：2,500.00元
   - 肉类：8,000.00元
   - 粮油类：3,200.00元
   - 调料类：800.00元
   - 合计：14,500.00元

2. 录入成本结转凭证：
   ```
   借：主营业务成本-食材成本    14,500.00
       贷：原材料-蔬菜类    2,500.00
           原材料-肉类    8,000.00
           原材料-粮油类    3,200.00
           原材料-调料类    800.00
   ```

#### 4.2 人工成本核算
**月末工资计提**：
1. 统计本月工资：
   - 厨师工资：12,000.00元
   - 帮厨工资：8,000.00元
   - 服务员工资：6,000.00元
   - 合计：26,000.00元

2. 录入工资计提凭证：
   ```
   借：主营业务成本-人工成本    26,000.00
       贷：应付职工薪酬-应付工资    26,000.00
   ```

### 第五部分：财务报表分析

#### 5.1 资产负债表
**路径**：财务管理 → 资产负债表

**主要项目分析**：
- **资产总计**：150,000.00元
  - 货币资金：50,000.00元
  - 原材料：15,000.00元
  - 固定资产：85,000.00元
- **负债总计**：30,000.00元
  - 应付账款：20,000.00元
  - 应付职工薪酬：10,000.00元
- **所有者权益**：120,000.00元

#### 5.2 利润表
**路径**：财务管理 → 利润表

**本月经营情况**：
- **营业收入**：80,000.00元
  - 学生餐费收入：60,000.00元
  - 教师餐费收入：20,000.00元
- **营业成本**：55,000.00元
  - 食材成本：35,000.00元
  - 人工成本：20,000.00元
- **毛利润**：25,000.00元
- **管理费用**：8,000.00元
- **净利润**：17,000.00元

#### 5.3 应付账款账龄分析
**路径**：财务管理 → 账龄分析

**账龄分布**：
- **30天以内**：15,000.00元（75%）
- **31-60天**：3,000.00元（15%）
- **61-90天**：1,500.00元（7.5%）
- **90天以上**：500.00元（2.5%）

**管理建议**：
- 及时支付30天内账款
- 关注超期账款，避免影响供应商关系
- 合理安排资金，保证现金流

### 第六部分：日常管理要点

#### 6.1 每日工作
**出纳人员**：
- 登记现金日记账
- 核对银行存款
- 处理收付款业务

**会计人员**：
- 审核原始凭证
- 录入记账凭证
- 核对往来账款

#### 6.2 每周工作
- 核对供应商对账单
- 分析食材消耗情况
- 检查库存异常

#### 6.3 每月工作
- 编制财务报表
- 进行成本核算
- 分析经营情况
- 制定下月预算

#### 6.4 注意事项
1. **凭证录入**：
   - 确保借贷平衡
   - 科目使用正确
   - 摘要描述清楚

2. **应付账款**：
   - 及时确认入库单
   - 核对供应商账单
   - 按时支付款项

3. **成本控制**：
   - 监控食材价格波动
   - 分析成本构成
   - 优化采购策略

4. **现金管理**：
   - 保证资金安全
   - 合理安排资金
   - 避免资金闲置

## 📞 常见问题解答

**Q1：入库单无法生成应付账款？**
A1：检查入库单是否已财务确认，确认供应商信息是否完整。

**Q2：凭证借贷不平衡？**
A2：检查明细行的借贷金额，确保借方总额等于贷方总额。

**Q3：财务报表数据不准确？**
A3：检查所有凭证是否已审核记账，确认期间设置是否正确。

**Q4：应付账款余额不对？**
A4：核对付款记录，检查是否有未确认的付款。

---

**使用建议**：建议先在测试环境熟悉操作流程，再在生产环境正式使用。如有疑问，请联系系统管理员。
