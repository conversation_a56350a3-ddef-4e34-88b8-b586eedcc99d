import os
from datetime import timedelta

basedir = os.path.abspath(os.path.dirname(__file__))

class Config:
    # 基本配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'hard-to-guess-string'

    # CSRF配置
    WTF_CSRF_ENABLED = False  # 禁用CSRF保护，使用系统自带令牌
    WTF_CSRF_TIME_LIMIT = 3600  # CSRF令牌有效期，单位秒
    WTF_CSRF_SSL_STRICT = False  # 禁用严格的SSL检查
    WTF_CSRF_METHODS = ['PUT', 'PATCH', 'DELETE']  # 需要CSRF保护的方法，移除POST
    WTF_CSRF_HEADERS = ['X-CSRFToken', 'X-CSRF-Token']  # CSRF令牌的请求头

    # 数据库配置
    # 使用远程SQL Server配置
    # 使用URL编码的连接字符串避免转义问题
    from urllib.parse import quote_plus
    # 添加DATETIME精度参数
    # 远程SQL Server配置
    conn_str = "DRIVER={SQL Server};SERVER=14.103.246.164;DATABASE=StudentsCMSSP;UID=StudentsCMSSP;PWD=***************"
    quoted_conn_str = quote_plus(conn_str)
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or f"mssql+pyodbc:///?odbc_connect={quoted_conn_str}"

    # 注意：SQLite配置已移除，系统将只使用SQL Server数据库

    SQLALCHEMY_TRACK_MODIFICATIONS = 0

    # 数据库连接池配置
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_recycle': 280,
        'pool_timeout': 20,
        'pool_size': 10,
        'max_overflow': 5,
    }

    # 上传文件配置
    UPLOAD_FOLDER = os.path.join(basedir, 'app/static/uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB

    # 会话配置
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)

    # 管理员配置
    ADMIN_USERNAME = os.environ.get('ADMIN_USERNAME') or 'admin'
    ADMIN_PASSWORD = os.environ.get('ADMIN_PASSWORD') or 'admin123'
    ADMIN_EMAIL = os.environ.get('ADMIN_EMAIL') or '<EMAIL>'

    # 分页配置
    ITEMS_PER_PAGE = 10

    # API配置
    API_KEY = os.environ.get('API_KEY') or 'system_fix_api_key_2025'

    # CSRF配置
    # 使用Flask-WTF的默认CSRF字段名
    # WTF_CSRF_FIELD_NAME = 'csrf_token'

    # Redis配置
    REDIS_HOST = os.environ.get('REDIS_HOST') or 'localhost'
    REDIS_PORT = int(os.environ.get('REDIS_PORT') or 6379)
    REDIS_DB = int(os.environ.get('REDIS_DB') or 0)
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD') or None

    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL') or 'INFO'
    LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    LOG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'logs', 'app.log')

    # 缓存配置
    CACHE_TYPE = 'redis'
    CACHE_REDIS_HOST = REDIS_HOST
    CACHE_REDIS_PORT = REDIS_PORT
    CACHE_REDIS_DB = REDIS_DB
    CACHE_REDIS_PASSWORD = REDIS_PASSWORD
    CACHE_DEFAULT_TIMEOUT = 300  # 5分钟

    @staticmethod
    def init_app(app):
        # 确保上传目录存在
        os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
        # 确保日志目录存在
        os.makedirs(os.path.dirname(app.config['LOG_FILE']), exist_ok=True)

class DevelopmentConfig(Config):
    DEBUG = True

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'

class ProductionConfig(Config):
    # 生产环境配置
    DEBUG = False
    TESTING = False

    # 生产环境应该使用环境变量设置敏感信息
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    REDIS_PASSWORD = os.environ.get('REDIS_PASSWORD')

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
