#!/usr/bin/env python3
"""
测试视频管理模块
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import VideoGuide
from sqlalchemy import text

def test_video_model():
    """测试视频模型"""
    app = create_app()
    
    with app.app_context():
        try:
            # 测试查询
            videos = VideoGuide.query.all()
            print(f"当前数据库中有 {len(videos)} 个视频")
            
            for video in videos:
                print(f"- {video.name} ({video.step_name})")
            
            # 测试创建
            test_video = VideoGuide(
                step_name='test',
                name='测试视频',
                description='这是一个测试视频',
                file_path='/static/videos/test/test.mp4'
            )
            
            # 使用原始SQL插入（避免ORM时间戳问题）
            sql = text("""
                INSERT INTO video_guides 
                (step_name, name, description, file_path)
                VALUES 
                (:step_name, :name, :description, :file_path)
            """)
            
            db.session.execute(sql, {
                'step_name': test_video.step_name,
                'name': test_video.name,
                'description': test_video.description,
                'file_path': test_video.file_path
            })
            db.session.commit()
            
            print("测试视频创建成功！")
            
            # 再次查询
            videos = VideoGuide.query.all()
            print(f"现在数据库中有 {len(videos)} 个视频")
            
        except Exception as e:
            print(f"测试失败: {str(e)}")
            db.session.rollback()

def test_database_connection():
    """测试数据库连接"""
    app = create_app()
    
    with app.app_context():
        try:
            # 测试基本连接
            result = db.session.execute(text("SELECT 1 as test"))
            print("数据库连接正常")
            
            # 检查表是否存在
            result = db.session.execute(text("""
                SELECT COUNT(*) as count 
                FROM INFORMATION_SCHEMA.TABLES 
                WHERE TABLE_NAME = 'video_guides'
            """))
            
            count = result.fetchone()[0]
            if count > 0:
                print("video_guides表存在")
            else:
                print("video_guides表不存在，请先执行迁移脚本")
                
        except Exception as e:
            print(f"数据库连接测试失败: {str(e)}")

if __name__ == '__main__':
    print("=== 视频管理模块测试 ===")
    
    print("\n1. 测试数据库连接...")
    test_database_connection()
    
    print("\n2. 测试视频模型...")
    test_video_model()
    
    print("\n测试完成！")
