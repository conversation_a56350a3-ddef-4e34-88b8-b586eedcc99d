/* 简化版主题颜色系统 - 去除过度效果 */
:root {
  /* 默认主题颜色 */
  --theme-primary: #007bff;
  --theme-primary-light: #66b3ff;
  --theme-primary-dark: #0056b3;
  --theme-primary-rgb: 0, 123, 255;

  /* 语义化颜色 */
  --theme-success: #28a745;
  --theme-success-light: #5cbf60;
  --theme-success-dark: #1e7e34;
  --theme-info: #17a2b8;
  --theme-info-light: #4fc3d7;
  --theme-info-dark: #117a8b;
  --theme-warning: #ffc107;
  --theme-warning-light: #ffcd39;
  --theme-warning-dark: #e0a800;
  --theme-danger: #dc3545;
  --theme-danger-light: #e66370;
  --theme-danger-dark: #bd2130;
  --theme-secondary: #6c757d;
  --theme-secondary-light: #868e96;
  --theme-secondary-dark: #545b62;

  /* 中性色调 */
  --theme-gray-100: #f8f9fa;
  --theme-gray-200: #e9ecef;
  --theme-gray-300: #dee2e6;
  --theme-gray-400: #ced4da;
  --theme-gray-500: #adb5bd;
  --theme-gray-600: #6c757d;
  --theme-gray-700: #495057;
  --theme-gray-800: #343a40;
  --theme-gray-900: #212529;

  /* 背景色 */
  --theme-surface: #ffffff;
  --theme-surface-dark: #f8f9fa;
}

/* 主题配色方案 */

/* 海洋蓝主题 */
[data-theme="primary"] {
  --theme-primary: #007bff;
  --theme-primary-light: #66b3ff;
  --theme-primary-dark: #0056b3;
  --theme-primary-rgb: 0, 123, 255;
}

/* 现代灰主题 */
[data-theme="secondary"] {
  --theme-primary: #6c757d;
  --theme-primary-light: #868e96;
  --theme-primary-dark: #545b62;
  --theme-primary-rgb: 108, 117, 125;
}

/* 自然绿主题 */
[data-theme="success"] {
  --theme-primary: #28a745;
  --theme-primary-light: #5cbf60;
  --theme-primary-dark: #1e7e34;
  --theme-primary-rgb: 40, 167, 69;
}

/* 活力橙主题 */
[data-theme="warning"] {
  --theme-primary: #ffc107;
  --theme-primary-light: #ffcd39;
  --theme-primary-dark: #e0a800;
  --theme-primary-rgb: 255, 193, 7;
}

/* 优雅紫主题 */
[data-theme="info"] {
  --theme-primary: #17a2b8;
  --theme-primary-light: #4fc3d7;
  --theme-primary-dark: #117a8b;
  --theme-primary-rgb: 23, 162, 184;
}

/* 深邃红主题 */
[data-theme="danger"] {
  --theme-primary: #dc3545;
  --theme-primary-light: #e66370;
  --theme-primary-dark: #bd2130;
  --theme-primary-rgb: 220, 53, 69;
}

/* 基础组件样式 */

/* 导航栏样式 */
.navbar {
  background-color: var(--theme-primary) !important;
  border: none;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  color: white !important;
  font-weight: 500;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  transition: color 0.2s ease;
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: white;
}

.btn-primary:hover {
  background-color: var(--theme-primary-dark);
  border-color: var(--theme-primary-dark);
  color: white;
}

.btn-primary:focus,
.btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.25);
}

.btn-primary:active {
  background-color: var(--theme-primary-dark);
  border-color: var(--theme-primary-dark);
}

/* 卡片样式 */
.card {
  border: 1px solid var(--theme-gray-200);
  border-radius: 0.375rem;
  background-color: var(--theme-surface);
}

.card-header {
  background-color: var(--theme-primary) !important;
  color: white !important;
  border-bottom: none !important;
  font-weight: 500;
}

.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6 {
  color: white !important;
  margin: 0;
}

/* 表单控件样式 */
.form-control {
  border: 1px solid var(--theme-gray-300);
  border-radius: 0.375rem;
  background-color: var(--theme-surface);
}

.form-control:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.15);
}

/* 徽章样式 */
.badge-primary {
  background-color: var(--theme-primary);
  color: white;
}

.badge-secondary {
  background-color: var(--theme-secondary);
  color: white;
}

.badge-success {
  background-color: var(--theme-success);
  color: white;
}

.badge-warning {
  background-color: var(--theme-warning);
  color: var(--theme-gray-800);
}

.badge-danger {
  background-color: var(--theme-danger);
  color: white;
}

.badge-info {
  background-color: var(--theme-info);
  color: white;
}

/* 链接样式 */
a {
  color: var(--theme-primary);
  text-decoration: none;
}

a:hover {
  color: var(--theme-primary-dark);
  text-decoration: underline;
}

/* 表格样式 */
.table-primary {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
}

.table-primary th,
.table-primary td {
  border-color: rgba(var(--theme-primary-rgb), 0.2);
}

/* 进度条样式 */
.progress-bar {
  background-color: var(--theme-primary);
}

/* 分页样式 */
.page-link {
  color: var(--theme-primary);
}

.page-link:hover {
  color: var(--theme-primary-dark);
  background-color: var(--theme-gray-200);
}

.page-item.active .page-link {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
}

/* 下拉菜单样式 */
.dropdown-item:hover,
.dropdown-item:focus {
  background-color: var(--theme-gray-100);
}

.dropdown-item.active {
  background-color: var(--theme-primary);
  color: white;
}

/* 警告框样式 */
.alert-primary {
  color: var(--theme-primary-dark);
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  border-color: rgba(var(--theme-primary-rgb), 0.2);
}

.alert-success {
  color: var(--theme-success-dark);
  background-color: rgba(40, 167, 69, 0.1);
  border-color: rgba(40, 167, 69, 0.2);
}

.alert-warning {
  color: var(--theme-warning-dark);
  background-color: rgba(255, 193, 7, 0.1);
  border-color: rgba(255, 193, 7, 0.2);
}

.alert-danger {
  color: var(--theme-danger-dark);
  background-color: rgba(220, 53, 69, 0.1);
  border-color: rgba(220, 53, 69, 0.2);
}

.alert-info {
  color: var(--theme-info-dark);
  background-color: rgba(23, 162, 184, 0.1);
  border-color: rgba(23, 162, 184, 0.2);
}



/* 11. 贵族典雅风 - 藏青色·深紫色·银色 */
[data-theme="noble-elegant"] {
  --theme-primary: #191970;
  --theme-primary-light: #483d8b;
  --theme-primary-dark: #0f0f4d;
  --theme-primary-rgb: 25, 25, 112;
  --theme-accent: #663399;
  --theme-surface: #f8f8ff;
  --theme-surface-dark: #f0f0ff;
  --theme-silver: #c0c0c0;
}







/* 15. 皇室庄重风 - 深紫色·深红色·黑色 */
[data-theme="royal-solemn"] {
  --theme-primary: #4b0082;
  --theme-primary-light: #663399;
  --theme-primary-dark: #301934;
  --theme-primary-rgb: 75, 0, 130;
  --theme-accent: #8b0000;
  --theme-surface: #f8f8ff;
  --theme-surface-dark: #2f2f2f;
  --theme-black: #000000;
}

/* === DeepSeek 现代配色方案系列 === */

/* 16. 深海科技蓝 - 冷色调渐变设计，科技感与现代感，SaaS/数据类网站首选 */
[data-theme="deep-sea-tech"] {
  --theme-primary: #2563EB; /* 主蓝色 */
  --theme-primary-light: #60a5fa; /* 浅蓝色 */
  --theme-primary-dark: #1d4ed8; /* 深蓝色 */
  --theme-primary-rgb: 37, 99, 235;
  --theme-secondary: #8B5CF6; /* 紫色 */
  --theme-accent: #06B6D4; /* 青色 */
  --theme-success: #10B981; /* 绿色 */
  --theme-surface: #F8FAFC; /* 背景色 */
  --theme-surface-dark: #F1F5F9;
  --theme-text: #1E293B; /* 文字色 */

  /* 渐变效果 */
  --gradient-primary: linear-gradient(135deg, #2563EB, #8B5CF6);
  --gradient-accent: linear-gradient(135deg, #06B6D4, #10B981);

  /* 科技感阴影 */
  --tech-shadow: 0 10px 30px rgba(37, 99, 235, 0.1);
  --tech-glow: 0 0 20px rgba(37, 99, 235, 0.3);
}

/* 17. 柔光莫兰迪 - 低饱和度撞色，柔和高级感，时尚创意行业 */
[data-theme="soft-morandi"] {
  --theme-primary: #6D28D9; /* 主紫色 */
  --theme-primary-light: #a855f7; /* 浅紫色 */
  --theme-primary-dark: #581c87; /* 深紫色 */
  --theme-primary-rgb: 109, 40, 217;
  --theme-secondary: #EC4899; /* 粉色 */
  --theme-accent: #F59E0B; /* 橙色 */
  --theme-info: #60A5FA; /* 蓝色 */
  --theme-surface: #FDF2F8; /* 背景色 */
  --theme-surface-dark: #FAE8FF;
  --theme-text: #334155; /* 文字色 */

  /* 莫兰迪渐变 */
  --gradient-primary: linear-gradient(135deg, #6D28D9, #EC4899);
  --gradient-accent: linear-gradient(135deg, #F59E0B, #60A5FA);

  /* 柔光效果 */
  --soft-shadow: 0 8px 25px rgba(109, 40, 217, 0.08);
  --soft-glow: 0 0 15px rgba(236, 72, 153, 0.2);
}

/* 18. 极简晨曦 - 高对比暖色，视觉冲击力强，电商/活动页面 */
[data-theme="minimal-dawn"] {
  --theme-primary: #F97316; /* 主橙色 */
  --theme-primary-light: #fb923c; /* 浅橙色 */
  --theme-primary-dark: #ea580c; /* 深橙色 */
  --theme-primary-rgb: 249, 115, 22;
  --theme-secondary: #EF4444; /* 红色 */
  --theme-accent: #3B82F6; /* 蓝色 */
  --theme-success: #22C55E; /* 绿色 */
  --theme-surface: #FFFFFF; /* 背景色 */
  --theme-surface-dark: #FFF7ED;
  --theme-text: #1F2937; /* 文字色 */

  /* 晨曦渐变 */
  --gradient-primary: linear-gradient(135deg, #F97316, #EF4444);
  --gradient-accent: linear-gradient(135deg, #3B82F6, #22C55E);

  /* 高对比效果 */
  --dawn-shadow: 0 12px 35px rgba(249, 115, 22, 0.15);
  --dawn-glow: 0 0 25px rgba(239, 68, 68, 0.4);
}

/* 19. 暗夜霓虹 - 深色背景+亮色，赛博朋克风格，游戏/元宇宙主题 */
[data-theme="dark-neon"] {
  --theme-primary: #8B5CF6; /* 主紫色 */
  --theme-primary-light: #a78bfa; /* 浅紫色 */
  --theme-primary-dark: #7c3aed; /* 深紫色 */
  --theme-primary-rgb: 139, 92, 246;
  --theme-secondary: #F43F5E; /* 粉红色 */
  --theme-accent: #06B6D4; /* 青色 */
  --theme-warning: #F59E0B; /* 橙色 */
  --theme-surface: #0F172A; /* 深色背景 */
  --theme-surface-dark: #1E293B;
  --theme-text: #E2E8F0; /* 浅色文字 */

  /* 霓虹渐变 */
  --gradient-primary: linear-gradient(135deg, #8B5CF6, #F43F5E);
  --gradient-accent: linear-gradient(135deg, #06B6D4, #F59E0B);

  /* 霓虹效果 */
  --neon-shadow: 0 0 30px rgba(139, 92, 246, 0.5);
  --neon-glow: 0 0 40px rgba(244, 63, 94, 0.6);
  --cyber-border: 1px solid rgba(139, 92, 246, 0.3);
}

/* 20. 自然生态绿 - 自然清新色调，治愈系风格，环保健康类网站 */
[data-theme="nature-eco"] {
  --theme-primary: #10B981; /* 主绿色 */
  --theme-primary-light: #34d399; /* 浅绿色 */
  --theme-primary-dark: #059669; /* 深绿色 */
  --theme-primary-rgb: 16, 185, 129;
  --theme-secondary: #0EA5E9; /* 蓝色 */
  --theme-accent: #F59E0B; /* 橙色 */
  --theme-warning: #F97316; /* 深橙色 */
  --theme-surface: #ECFDF5; /* 背景色 */
  --theme-surface-dark: #D1FAE5;
  --theme-text: #1E293B; /* 文字色 */

  /* 自然渐变 */
  --gradient-primary: linear-gradient(135deg, #10B981, #0EA5E9);
  --gradient-accent: linear-gradient(135deg, #F59E0B, #F97316);

  /* 自然效果 */
  --nature-shadow: 0 8px 20px rgba(16, 185, 129, 0.12);
  --nature-glow: 0 0 18px rgba(14, 165, 233, 0.25);
  --eco-border: 1px solid rgba(16, 185, 129, 0.2);
}

/* 专业界面元素样式应用 */

/* 导航栏样式 - 增强优先级 */
.navbar,
.navbar.bg-primary,
.navbar.bg-secondary,
.navbar.bg-success,
.navbar.bg-warning,
.navbar.bg-info,
.navbar.bg-danger,
.navbar.bg-dark,
.navbar.bg-classic-neutral,
.navbar.bg-modern-neutral,
.navbar.bg-royal-solemn,
.navbar.bg-deep-sea-tech,
.navbar.bg-soft-morandi,
.navbar.bg-minimal-dawn,
.navbar.bg-dark-neon,
.navbar.bg-nature-eco {
  background-color: var(--theme-primary) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: none;
  z-index: 1030; /* 确保导航栏在最上层 */
}

.navbar-brand {
  color: white !important;
  font-weight: 500 !important; /* 使用中等字体权重 */
  font-size: 1.25rem;
}

.navbar-nav .nav-link {
  color: rgba(255, 255, 255, 0.9) !important;
  font-weight: 400 !important; /* 使用正常字体权重 */
  font-size: 14px !important; /* 统一导航栏字体大小 */
  transition: all 0.3s ease;
  border-radius: 6px;
  margin: 0 2px;
  padding: 8px 12px !important;
}

.navbar-nav .nav-link:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1);
  /* 移除transform效果，避免文字模糊 */
}

.navbar-nav .nav-link.active {
  background-color: rgba(255, 255, 255, 0.2) !important;
  color: white !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--theme-primary);
  border: none;
  border-radius: 8px;
  font-weight: normal !important;
  padding: 10px 20px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 4px rgba(var(--theme-primary-rgb), 0.2);
}

.btn-primary:hover {
  background-color: var(--theme-primary-dark);
  /* 移除transform效果，避免文字模糊 */
  box-shadow: 0 3px 6px rgba(var(--theme-primary-rgb), 0.3);
}

.btn-primary:focus,
.btn-primary.focus {
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.5);
}

.btn-primary:active {
  /* 移除transform效果，避免文字模糊 */
  box-shadow: 0 2px 4px rgba(var(--theme-primary-rgb), 0.3);
}

/* 卡片样式 */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  background-color: var(--theme-surface, #ffffff);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

/* === 统一卡片标题样式 - 修复颜色对比度和字体大小问题 === */
.card-header {
  background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-light, #60a5fa) 100%) !important;
  color: white !important;
  border: none !important;
  border-radius: 8px 8px 0 0 !important;
  font-weight: 500 !important; /* 使用中等字体权重，提高可读性 */
  font-size: 14px !important; /* 统一字体大小为14px */
  padding: 0.75rem 1rem !important; /* 减小内边距，更紧凑 */
  line-height: 1.4 !important;
  /* text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1) !important; */ /* 注释掉文字阴影，避免模糊效果 */
}

/* 卡片标题内的所有文字元素统一样式 */
.card-header h1,
.card-header h2,
.card-header h3,
.card-header h4,
.card-header h5,
.card-header h6,
.card-header .h1,
.card-header .h2,
.card-header .h3,
.card-header .h4,
.card-header .h5,
.card-header .h6 {
  color: white !important;
  font-size: 14px !important; /* 统一所有标题大小 */
  font-weight: 500 !important; /* 使用中等字体权重 */
  margin: 0 !important;
  line-height: 1.4 !important;
}

/* 特殊背景色类的处理 */
.card-header.bg-primary {
  background: linear-gradient(135deg, var(--theme-primary, #2563eb) 0%, var(--theme-primary-light, #60a5fa) 100%) !important;
  color: white !important;
}

/* 表单控件样式 */
.form-control {
  border: 1px solid var(--theme-gray-200, #ced4da);
  border-radius: 0.375rem;
  transition: all 0.3s ease;
  background-color: var(--theme-surface, #ffffff);
}

.form-control:focus {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 0.2rem rgba(var(--theme-primary-rgb), 0.15);
  background-color: var(--theme-surface, #ffffff);
}

/* 徽章样式 */
.badge-primary {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

/* 补充其他badge样式，确保颜色对比度 */
.badge-secondary {
  background-color: #6c757d;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-success {
  background-color: #28a745;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-danger {
  background-color: #dc3545;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529; /* 深色文字确保对比度 */
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-info {
  background-color: #17a2b8;
  color: white;
  border-radius: 6px;
  font-weight: normal !important;
  padding: 6px 10px;
}

.badge-light {
  background-color: #f8f9fa;
  color: #212529; /* 深色文字 */
  border-radius: 6px;
  font-weight: 500;
  padding: 6px 10px;
}

.badge-dark {
  background-color: #343a40;
  color: white;
  border-radius: 6px;
  font-weight: 500;
  padding: 6px 10px;
}

/* 文本颜色 */
.text-primary {
  color: var(--theme-primary) !important;
}

/* 边框颜色 */
.border-primary {
  border-color: var(--theme-primary) !important;
}

/* 链接样式 */
a {
  color: var(--theme-primary);
  text-decoration: none;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--theme-primary-dark);
  text-decoration: none;
}

/* 下拉菜单样式 */
.dropdown-menu {
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  padding: 8px;
  background-color: var(--theme-surface, #ffffff);
}

.dropdown-item {
  border-radius: 8px;
  padding: 10px 16px;
  transition: all 0.3s ease;
  font-weight: 500;
}

.dropdown-item:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  color: var(--theme-primary);
  transform: translateX(4px);
}

.dropdown-item.active,
.dropdown-item:active {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  color: white;
}

/* 暗夜霓虹主题特殊样式 */
[data-theme="dark-neon"] {
  /* 深色背景下的特殊处理 */
  background-color: var(--theme-surface) !important;
  color: var(--theme-text) !important;
}

[data-theme="dark-neon"] .card {
  background-color: var(--theme-surface-dark) !important;
  color: var(--theme-text) !important;
  border: var(--cyber-border) !important;
  box-shadow: var(--neon-shadow) !important;
}

[data-theme="dark-neon"] .card-body {
  background-color: var(--theme-surface-dark) !important;
  color: var(--theme-text) !important;
}

[data-theme="dark-neon"] .form-control {
  background-color: var(--theme-surface-dark) !important;
  color: var(--theme-text) !important;
  border: var(--cyber-border) !important;
}

[data-theme="dark-neon"] .form-control:focus {
  box-shadow: var(--neon-glow) !important;
}

[data-theme="dark-neon"] .dropdown-menu {
  background-color: var(--theme-surface-dark) !important;
  color: var(--theme-text) !important;
  border: var(--cyber-border) !important;
  box-shadow: var(--neon-shadow) !important;
}

[data-theme="dark-neon"] .dropdown-item {
  color: var(--theme-text) !important;
}

[data-theme="dark-neon"] .dropdown-item:hover {
  background-color: rgba(139, 92, 246, 0.2) !important;
  color: var(--theme-primary) !important;
}

/* 暗夜霓虹主题表格样式 */
[data-theme="dark-neon"] .table {
  background-color: var(--theme-surface-dark) !important;
  color: var(--theme-text) !important;
  border: var(--cyber-border) !important;
}

[data-theme="dark-neon"] .table th {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-secondary) 100%) !important;
  color: white !important;
  border: var(--cyber-border) !important;
  box-shadow: var(--neon-shadow) !important;
}

[data-theme="dark-neon"] .table td {
  background-color: var(--theme-surface-dark) !important;
  color: var(--theme-text) !important;
  border: var(--cyber-border) !important;
}

[data-theme="dark-neon"] .table tbody tr:hover {
  background-color: rgba(139, 92, 246, 0.1) !important;
  box-shadow: var(--neon-glow) !important;
}

/* 暗夜霓虹主题按钮样式 */
[data-theme="dark-neon"] .btn-primary {
  background: var(--gradient-primary) !important;
  border: var(--cyber-border) !important;
  color: white !important;
  box-shadow: var(--neon-shadow) !important;
}

[data-theme="dark-neon"] .btn-primary:hover {
  box-shadow: var(--neon-glow) !important;
  transform: translateY(-2px);
}

/* 暗夜霓虹主题导航栏样式 */
[data-theme="dark-neon"] .navbar {
  background: var(--theme-surface) !important;
  border-bottom: 1px solid rgba(139, 92, 246, 0.3) !important;
  box-shadow: 0 2px 10px rgba(139, 92, 246, 0.2) !important;
}

[data-theme="dark-neon"] .navbar-nav .nav-link {
  color: var(--theme-text) !important;
}

[data-theme="dark-neon"] .navbar-nav .nav-link:hover {
  color: var(--theme-primary) !important;
  /* text-shadow: 0 0 5px rgba(139, 92, 246, 0.5) !important; */ /* 注释掉霓虹发光效果，避免模糊 */
}

/* 暗夜霓虹主题页脚样式 */
[data-theme="dark-neon"] .footer {
  background-color: var(--theme-surface-dark) !important;
  color: var(--theme-text) !important;
  border-top: 1px solid rgba(139, 92, 246, 0.3) !important;
}

/* 表格样式 */
.table {
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.table thead {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
}

.table thead th {
  background: transparent;
  color: white !important;
  border: none;
  font-weight: 500 !important; /* 使用中等字体权重 */
  font-size: 14px !important; /* 统一表头字体大小 */
  padding: 12px 16px;
  transition: none;
  /* text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1); */ /* 注释掉文字阴影，避免模糊效果 */
}

/* 最后一列表头不需要右边框 */
.table thead th:last-child {
  border-right: none;
}

.table tbody tr {
  transition: all 0.3s ease;
}

.table tbody tr:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.05);
  /* 移除缩放效果，保持简洁 */
}

.table-primary {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
}

/* 分页样式 */
.pagination {
  gap: 4px;
}

.pagination .page-link {
  color: var(--theme-primary);
  border: 2px solid var(--theme-gray-200, #e5e7eb);
  border-radius: 8px;
  padding: 10px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.pagination .page-link:hover {
  background-color: var(--theme-primary);
  border-color: var(--theme-primary);
  color: white;
  transform: translateY(-2px);
}

.pagination .page-item.active .page-link {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-dark) 100%);
  border-color: var(--theme-primary);
  color: white;
  box-shadow: 0 4px 8px rgba(var(--theme-primary-rgb), 0.3);
}

/* 警告框样式 */
.alert {
  border: none;
  border-radius: 12px;
  padding: 16px 20px;
  font-weight: 500;
}

.alert-primary {
  background: linear-gradient(135deg, rgba(var(--theme-primary-rgb), 0.1) 0%, rgba(var(--theme-primary-rgb), 0.05) 100%);
  color: var(--theme-primary-dark);
  border-left: 4px solid var(--theme-primary);
}

/* 进度条样式 */
.progress {
  height: 8px;
  border-radius: 8px;
  background-color: var(--theme-gray-200, #e5e7eb);
}

.progress-bar {
  background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
  border-radius: 8px;
  transition: all 0.3s ease;
}

/* 图标样式 */
.text-primary i {
  color: var(--theme-primary) !important;
}

/* === 基于您分析的高级功能 === */

/* 1. 动态响应机制 - 环境光自动调节 */
@media (prefers-color-scheme: dark) {
  :root {
    --auto-saturation: 0.8; /* 夜间降低饱和度 */
  }
}

@media (prefers-color-scheme: light) {
  :root {
    --auto-saturation: 1.0; /* 日间正常饱和度 */
  }
}

/* 2. 无障碍设计 - 色弱用户支持 */
@media (prefers-contrast: high) {
  :root {
    --theme-primary: color-mix(in srgb, var(--theme-primary) 80%, black 20%);
    --contrast-boost: 1.5;
  }
}

/* 3. 应急对比度切换 */
.emergency-contrast {
  --theme-primary: #000000 !important;
  --theme-surface: #ffffff !important;
  --theme-text: #000000 !important;
  filter: contrast(2) !important;
}

/* 4. 自然绿主题色弱增强模式 */
[data-theme="success"].colorblind-enhanced {
  --theme-primary: #7CB342; /* 黄绿色增强 */
  --theme-accent: #FF9800; /* 橙色对比 */
}

/* 5. 移动端拇指热区配色权重 */
@media (max-width: 768px) {
  .btn-primary,
  .nav-link,
  .dropdown-item {
    min-height: 44px; /* 符合拇指触控标准 */
    background: linear-gradient(135deg, var(--theme-primary) 0%, var(--theme-primary-light) 100%);
    box-shadow: 0 2px 8px rgba(var(--theme-primary-rgb), 0.3);
  }
}

/* 6. 文化适配 - 避免不当配色组合 */
.cultural-safe {
  /* 避免大面积黑白配 (东亚市场) */
  background: var(--theme-surface);
  color: var(--theme-primary);
}

/* 7. 用友主题专业提示 */
[data-theme="yonyou"] .professional-tip {
  background: rgba(30, 136, 229, 0.1);
  color: var(--theme-primary);
  border: 1px solid rgba(30, 136, 229, 0.2);
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 0.9rem;
}

/* 全局动画 - 优化版 */
* {
  /* transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, transform 0.3s ease; */ /* 注释掉过渡效果，主题切换更直接 */
}

/* 用友主题特殊处理 */
[data-theme="yonyou"] {
  --bs-body-bg: #F5F7FA;
  --bs-body-color: #212121;
}

[data-theme="yonyou"] .card {
  background-color: #FFFFFF;
  color: #212121;
  border: 1px solid #E0E0E0;
  box-shadow: var(--yonyou-card-shadow);
}

/* === 主题特定动画效果 === */

/* 移除表格背景动画 - 保持简洁专业 */

/* 现代主题 - 霓虹发光效果 */
[data-theme="secondary"] .btn-primary {
  box-shadow: var(--glow-effect);
  border: 1px solid var(--theme-neon-blue);
}

[data-theme="secondary"] .btn-primary:hover {
  animation: neonPulse 1.5s ease-in-out infinite alternate;
}

@keyframes neonPulse {
  from { box-shadow: 0 0 5px var(--theme-neon-blue); }
  to { box-shadow: 0 0 20px var(--theme-neon-blue), 0 0 30px var(--theme-neon-blue); }
}

/* 自然绿主题 - 保持简洁，移除过度动画 */
[data-theme="success"] .btn:active {
  animation: rippleEffect 0.6s ease-out;
}

@keyframes rippleEffect {
  0% { box-shadow: 0 0 0 0 var(--ripple-color); }
  70% { box-shadow: 0 0 0 10px rgba(74, 120, 86, 0); }
  100% { box-shadow: 0 0 0 0 rgba(74, 120, 86, 0); }
}

/* 活力橙主题 - 火焰燃烧效果 */
[data-theme="warning"] .progress-bar {
  background: var(--flame-gradient);
  animation: flameFlicker 2s ease-in-out infinite;
}

@keyframes flameFlicker {
  0%, 100% { filter: hue-rotate(0deg) brightness(1); }
  25% { filter: hue-rotate(10deg) brightness(1.1); }
  50% { filter: hue-rotate(-5deg) brightness(0.9); }
  75% { filter: hue-rotate(5deg) brightness(1.05); }
}

[data-theme="warning"] .btn-primary:hover {
  box-shadow: var(--energy-pulse);
}

/* 优雅紫主题 - VIP烫金效果 */
[data-theme="info"] .badge-primary {
  background: var(--vip-gradient);
  box-shadow: var(--gold-glow);
  animation: goldShimmer 3s ease-in-out infinite;
}

@keyframes goldShimmer {
  0%, 100% { filter: brightness(1); }
  50% { filter: brightness(1.2); }
}

/* 深邃红主题 - 金属光泽效果 */
[data-theme="danger"] .btn-primary {
  background: var(--theme-metallic-sheen);
  color: var(--theme-text-on-primary);
}

/* 用友主题 - 专业阴影效果 */
[data-theme="yonyou"] .btn-primary:focus {
  box-shadow: var(--yonyou-shadow);
}

[data-theme="yonyou"] .navbar {
  box-shadow: 0 2px 8px rgba(30, 136, 229, 0.15);
}

[data-theme="yonyou"] .table {
  border: 1px solid #E0E0E0;
}

[data-theme="yonyou"] .table th {
  background-color: #E3F2FD;
  color: #1565C0;
  border: 1px solid #BBDEFB;
}

[data-theme="yonyou"] .table tbody tr:hover {
  background-color: var(--theme-hover);
}

/* 复古典雅风 - 移除模糊效果，保持清晰 */
[data-theme="vintage-elegant"] .card {
  font-family: var(--serif-font);
  background-color: var(--theme-surface);
}

[data-theme="vintage-elegant"] .btn {
  /* 移除文字阴影，保持文字清晰 */
  text-shadow: none;
  font-weight: 600; /* 增加字重提高可读性 */
}

/* === 响应式动画优化 === */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* === 性能优化 === */
.card,
.btn,
.navbar {
  will-change: transform, box-shadow;
}

/* === 主题切换动画 === */
[data-theme] {
  /* transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1); */ /* 注释掉主题切换过渡，立即生效 */
}

/* === 季度色彩偏好调研提示 === */
.theme-feedback-prompt {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: var(--theme-primary);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  animation: slideInUp 0.5s ease-out;
}

@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

[data-theme="dark"] .navbar {
  background-color: var(--theme-primary) !important;
}

[data-theme="dark"] .table {
  color: #fff;
}



/* 响应式调整 */
@media (max-width: 768px) {
  .navbar-nav .nav-link.active {
    border-radius: 0.25rem;
    margin: 0.25rem;
  }
}

/* 主题切换按钮样式 */
.theme-selector {
  position: relative;
}

.theme-preview {
  display: inline-block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  margin-right: 8px;
  border: 2px solid #fff;
  box-shadow: 0 1px 3px rgba(0,0,0,0.3);
  vertical-align: middle;
}

/* 主题预览颜色 - 与实际主题配色完全匹配 */
.theme-preview.primary {
  background: linear-gradient(135deg, #001F3F 0%, #001122 100%);
  box-shadow: 0 2px 4px rgba(0, 31, 63, 0.3);
}
.theme-preview.secondary {
  background: linear-gradient(135deg, #333333 0%, #1a1a1a 100%);
  box-shadow: 0 2px 4px rgba(51, 51, 51, 0.3);
}
.theme-preview.success {
  background: linear-gradient(135deg, #4A7856 0%, #2d4a35 100%);
  box-shadow: 0 2px 4px rgba(74, 120, 86, 0.3);
}
.theme-preview.warning {
  background: linear-gradient(135deg, #FF6B35 0%, #e55a2b 100%);
  box-shadow: 0 2px 4px rgba(255, 107, 53, 0.3);
}
.theme-preview.info {
  background: linear-gradient(135deg, #B399D4 0%, #7851A9 100%);
  box-shadow: 0 2px 4px rgba(179, 153, 212, 0.3);
}
.theme-preview.danger {
  background: linear-gradient(135deg, #C91F37 0%, #900020 100%);
  box-shadow: 0 2px 4px rgba(201, 31, 55, 0.3);
}


/* 经典配色方案预览 - 与实际主题配色完全匹配 */
.theme-preview.classic-neutral {
  background: linear-gradient(135deg, #8b4513 0%, #654321 100%);
  box-shadow: 0 2px 4px rgba(139, 69, 19, 0.3);
}
.theme-preview.modern-neutral {
  background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%);
  box-shadow: 0 2px 4px rgba(74, 85, 104, 0.3);
}

.theme-preview.royal-solemn {
  background: linear-gradient(135deg, #4b0082 0%, #301934 100%);
  box-shadow: 0 2px 4px rgba(75, 0, 130, 0.3);
}

/* 主题切换器下拉菜单样式 - 增强版 */
#themeDropdown {
  color: rgba(255, 255, 255, 0.9) !important;
  transition: all 0.3s ease;
  position: relative;
}

#themeDropdown:hover {
  color: white !important;
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-radius: 0.25rem;
  transform: scale(1.05);
}

/* 主题切换面板样式 */
.theme-switcher-panel {
  background: linear-gradient(135deg, rgba(255,255,255,0.95) 0%, rgba(248,249,250,0.95) 100%) !important;
  border: 1px solid rgba(0,0,0,0.1) !important;
  box-shadow: 0 10px 40px rgba(0,0,0,0.15) !important;
  backdrop-filter: blur(20px) !important;
  animation: themeDropdownSlide 0.3s ease-out;
  transform-origin: top left;
}

[data-theme="dark"] .theme-switcher-panel,
[data-theme="dark-neon"] .theme-switcher-panel {
  background: linear-gradient(135deg, rgba(45,55,72,0.95) 0%, rgba(26,32,44,0.95) 100%) !important;
  border: 1px solid rgba(255,255,255,0.1) !important;
  color: white !important;
}

@keyframes themeDropdownSlide {
  from {
    opacity: 0;
    transform: translateX(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateX(0) scale(1);
  }
}

.theme-option {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 2px 4px;
  position: relative;
  overflow: hidden;
}

.theme-option::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, var(--theme-primary) 0%, transparent 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.theme-option:hover {
  background-color: rgba(var(--theme-primary-rgb), 0.1);
  text-decoration: none;
  transform: translateX(5px);
  box-shadow: 0 4px 12px rgba(var(--theme-primary-rgb), 0.2);
}

.theme-option:hover::before {
  opacity: 0.1;
}

.theme-option.active {
  background: linear-gradient(90deg, var(--theme-primary) 0%, rgba(var(--theme-primary-rgb), 0.8) 100%);
  color: white;
  transform: translateX(8px);
  box-shadow: 0 6px 20px rgba(var(--theme-primary-rgb), 0.4);
}

.theme-option.active .theme-preview {
  border-color: white;
  box-shadow: 0 0 15px rgba(255,255,255,0.5);
}

/* 主题收藏功能 */
.theme-option .theme-favorite {
  margin-left: auto;
  opacity: 0;
  transition: all 0.3s ease;
  cursor: pointer;
  color: #ffc107;
  font-size: 0.9rem;
}

.theme-option:hover .theme-favorite {
  opacity: 1;
  transform: scale(1.1);
}

.theme-option.favorited .theme-favorite {
  opacity: 1;
  color: #ffc107 !important;
  animation: starPulse 2s ease-in-out infinite;
}

@keyframes starPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 主题控制面板样式 */
.theme-controls {
  background: rgba(0,0,0,0.05);
  border-radius: 8px;
  margin: 4px;
}

[data-theme="dark"] .theme-controls,
[data-theme="dark-neon"] .theme-controls {
  background: rgba(255,255,255,0.1);
}

.theme-controls .btn {
  min-width: 36px;
  height: 32px;
  border-radius: 6px;
  transition: all 0.3s ease;
}

.theme-controls .btn:hover {
  transform: scale(1.05);
}

/* 主题切换全局动画 */
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
}

/* 主题切换时的页面过渡效果 */
body[data-theme] {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 增强的主题预览样式 */
.theme-preview {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 12px;
  border: 2px solid rgba(255,255,255,0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.theme-preview::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255,255,255,0.3);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

.theme-option:hover .theme-preview::before {
  width: 100%;
  height: 100%;
}

.theme-option.active .theme-preview {
  border-color: white;
  box-shadow: 0 0 15px rgba(255,255,255,0.6);
  transform: scale(1.1);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .theme-switcher-panel {
    min-width: 260px !important;
    max-width: 90vw !important;
    left: auto !important;
    right: 0 !important;
  }

  .theme-option {
    padding: 0.6rem 0.8rem;
  }

  .theme-preview {
    width: 20px;
    height: 20px;
    margin-right: 10px;
  }

  .theme-controls {
    padding: 8px 12px !important;
  }
}

/* 主题切换器图标动画 */
#themeDropdown {
  position: relative;
  overflow: hidden;
}

#themeDropdown::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255,255,255,0.2);
  border-radius: 50%;
  transition: all 0.3s ease;
  transform: translate(-50%, -50%);
}

#themeDropdown:hover::before {
  width: 100%;
  height: 100%;
}

/* 下拉菜单分组标题样式 */
.dropdown-header {
  font-weight: 600;
  font-size: 0.85rem;
  color: var(--theme-primary) !important;
  padding: 0.75rem 1rem 0.5rem;
  border-bottom: 1px solid rgba(0,0,0,0.1);
  margin-bottom: 0.25rem;
}

[data-theme="dark"] .dropdown-header,
[data-theme="dark-neon"] .dropdown-header {
  color: rgba(255,255,255,0.9) !important;
  border-bottom-color: rgba(255,255,255,0.1);
}

/* 分割线样式 */
.dropdown-divider {
  margin: 0.5rem 0;
  border-color: rgba(0,0,0,0.1);
}

[data-theme="dark"] .dropdown-divider,
[data-theme="dark-neon"] .dropdown-divider {
  border-color: rgba(255,255,255,0.1);
}

/* 主题统计弹窗样式 */
.theme-stats-popup {
  border-radius: 12px !important;
  box-shadow: 0 20px 60px rgba(0,0,0,0.3) !important;
}

.theme-stats-popup .swal2-html-container {
  padding: 0 !important;
}

/* 主题切换器快捷键提示 */
.theme-shortcut-hint {
  position: absolute;
  bottom: -25px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 0.7rem;
  color: rgba(255,255,255,0.6);
  background: rgba(0,0,0,0.8);
  padding: 2px 6px;
  border-radius: 4px;
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

#themeDropdown:hover .theme-shortcut-hint {
  opacity: 1;
}

/* 主题切换器加载状态 */
.theme-switcher-loading {
  position: relative;
  pointer-events: none;
}

.theme-switcher-loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255,255,255,0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% { transform: translate(-50%, -50%) rotate(0deg); }
  100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* 主题切换成功动画 */
.theme-switch-success {
  animation: themeSuccessPulse 0.6s ease-out;
}

@keyframes themeSuccessPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* 主题兼容性警告样式 */
.theme-compatibility-warning {
  position: fixed;
  top: 60px;
  right: 20px;
  background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
  color: white;
  padding: 12px 16px;
  border-radius: 8px;
  z-index: 9998;
  max-width: 300px;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
  animation: slideInRight 0.3s ease-out;
}

.theme-compatibility-warning .close-btn {
  position: absolute;
  top: 4px;
  right: 8px;
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  line-height: 1;
}

/* 深色模式使用时间提醒样式优化 */
.dark-mode-warning {
  animation: darkModeWarningSlide 0.5s ease-out;
}

@keyframes darkModeWarningSlide {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 主题切换器性能优化 */
.theme-switcher-panel.optimized {
  will-change: transform, opacity;
  transform: translateZ(0);
}

/* 主题切换器无障碍增强 */
.theme-option:focus {
  outline: 2px solid var(--theme-primary);
  outline-offset: 2px;
}

.theme-option[aria-selected="true"] {
  background: var(--theme-primary) !important;
  color: white !important;
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .theme-switcher-panel {
    border: 2px solid currentColor !important;
    background: Canvas !important;
    color: CanvasText !important;
  }

  .theme-option {
    border: 1px solid currentColor;
  }

  .theme-option:hover,
  .theme-option:focus {
    background: Highlight !important;
    color: HighlightText !important;
  }
}

/* 主题切换器在系统设置页面的样式 */
.theme-color-selector {
  position: relative;
}

.theme-color-selector option {
  padding: 0.5rem;
}

/* 主题切换动画增强 */
.navbar-nav .nav-link {
  transition: color 0.3s ease, background-color 0.3s ease;
}

.dropdown-menu {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

/* 主题预览提示样式 */
.theme-preview-toast {
  background: linear-gradient(45deg, var(--theme-primary), var(--theme-primary-light));
  color: white;
  border: none;
}

/* 响应式主题切换器 */
@media (max-width: 768px) {
  .theme-preview {
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }

  .theme-option {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }
}

/* === DeepSeek 现代系列主题预览样式 === */

/* 深海科技蓝 - 冷色调渐变设计，科技感与现代感 */
.theme-preview.deep-sea-tech {
  background: linear-gradient(135deg, #2563EB 0%, #8B5CF6 100%);
  box-shadow: 0 0 10px rgba(37, 99, 235, 0.3), 0 2px 4px rgba(139, 92, 246, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

/* 柔光莫兰迪 - 低饱和度撞色，柔和高级感 */
.theme-preview.soft-morandi {
  background: linear-gradient(135deg, #6D28D9 0%, #EC4899 100%);
  box-shadow: 0 0 8px rgba(109, 40, 217, 0.25), 0 2px 4px rgba(236, 72, 153, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

/* 极简晨曦 - 高对比暖色，视觉冲击力强 */
.theme-preview.minimal-dawn {
  background: linear-gradient(135deg, #F97316 0%, #EF4444 100%);
  box-shadow: 0 0 12px rgba(249, 115, 22, 0.4), 0 2px 4px rgba(239, 68, 68, 0.3);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

/* 暗夜霓虹 - 深色背景+亮色，赛博朋克风格 */
.theme-preview.dark-neon {
  background: linear-gradient(135deg, #8B5CF6 0%, #F43F5E 100%);
  box-shadow: 0 0 15px rgba(139, 92, 246, 0.5), 0 0 20px rgba(244, 63, 94, 0.4);
  border: 2px solid rgba(255, 255, 255, 0.9);
  animation: neonGlow 2s ease-in-out infinite alternate;
}

@keyframes neonGlow {
  from {
    box-shadow: 0 0 15px rgba(139, 92, 246, 0.5), 0 0 20px rgba(244, 63, 94, 0.4);
  }
  to {
    box-shadow: 0 0 20px rgba(139, 92, 246, 0.7), 0 0 30px rgba(244, 63, 94, 0.6);
  }
}

/* 自然生态绿 - 自然清新色调，治愈系风格 */
.theme-preview.nature-eco {
  background: linear-gradient(135deg, #10B981 0%, #0EA5E9 100%);
  box-shadow: 0 0 8px rgba(16, 185, 129, 0.3), 0 2px 4px rgba(14, 165, 233, 0.25);
  border: 2px solid rgba(255, 255, 255, 0.9);
}

/* DeepSeek 主题特殊效果增强 */
.theme-option:hover .theme-preview.deep-sea-tech {
  transform: scale(1.1);
  box-shadow: 0 0 15px rgba(37, 99, 235, 0.5), 0 4px 8px rgba(139, 92, 246, 0.3);
}

.theme-option:hover .theme-preview.soft-morandi {
  transform: scale(1.1);
  box-shadow: 0 0 12px rgba(109, 40, 217, 0.4), 0 4px 8px rgba(236, 72, 153, 0.3);
}

.theme-option:hover .theme-preview.minimal-dawn {
  transform: scale(1.1);
  box-shadow: 0 0 18px rgba(249, 115, 22, 0.6), 0 4px 8px rgba(239, 68, 68, 0.4);
}

.theme-option:hover .theme-preview.dark-neon {
  transform: scale(1.1);
  animation-duration: 1s;
}

.theme-option:hover .theme-preview.nature-eco {
  transform: scale(1.1);
  box-shadow: 0 0 12px rgba(16, 185, 129, 0.5), 0 4px 8px rgba(14, 165, 233, 0.4);
}

/* DeepSeek 主题预览过渡动画 */
.theme-preview.deep-sea-tech,
.theme-preview.soft-morandi,
.theme-preview.minimal-dawn,
.theme-preview.dark-neon,
.theme-preview.nature-eco {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
