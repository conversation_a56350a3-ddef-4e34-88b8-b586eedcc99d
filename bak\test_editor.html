<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试专业编辑器</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="app/static/financial/css/voucher-editor.css">
</head>
<body>
    <div class="container-fluid">
        <h2>专业编辑器测试</h2>
        
        <!-- 专业编辑器容器 -->
        <div class="voucher-editor-container">
            <div class="voucher-table-section">
                <!-- 编辑器将在这里渲染 -->
                <div id="loading-indicator" style="text-align: center; padding: 20px; color: #666;">
                    <i class="fas fa-spinner fa-spin"></i> 正在加载编辑器...
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="app/static/financial/js/professional-voucher-editor.js"></script>
    
    <script>
        console.log('测试页面加载完成');
        
        // 模拟数据
        const mockSubjects = [
            {id: 1, code: '1001', name: '库存现金'},
            {id: 2, code: '1002', name: '银行存款'},
            {id: 3, code: '2001', name: '应付账款'}
        ];
        
        const mockDetails = [
            {
                id: 1,
                line_number: 1,
                subject_id: 1,
                subject: {id: 1, code: '1001', name: '库存现金'},
                summary: '收到现金',
                debit_amount: 1000.00,
                credit_amount: 0.00
            },
            {
                id: 2,
                line_number: 2,
                subject_id: 3,
                subject: {id: 3, code: '2001', name: '应付账款'},
                summary: '应付款项',
                debit_amount: 0.00,
                credit_amount: 1000.00
            }
        ];
        
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM加载完成，开始测试编辑器');
            
            try {
                // 创建编辑器实例
                const editor = new ProfessionalVoucherEditor(8, {
                    autoSave: false,
                    enableKeyboardNavigation: true,
                    enableAutocomplete: true
                });
                
                // 模拟数据加载
                editor.subjects = mockSubjects;
                editor.data = mockDetails;
                
                // 渲染表格
                editor.renderTable();
                
                console.log('编辑器测试成功');
                
            } catch (error) {
                console.error('编辑器测试失败:', error);
                alert('编辑器测试失败: ' + error.message);
            }
        });
    </script>
</body>
</html>
