#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高性能启动脚本
优化启动速度和运行性能
"""

import os
import sys
from app import create_app
from performance_config import DevelopmentPerformanceConfig
from werkzeug.middleware.proxy_fix import ProxyFix

def create_optimized_app():
    """创建优化的应用实例"""
    
    # 设置环境变量优化Python性能
    os.environ['PYTHONOPTIMIZE'] = '1'  # 启用Python优化
    os.environ['PYTHONDONTWRITEBYTECODE'] = '1'  # 不生成.pyc文件
    
    # 创建应用
    app = create_app(DevelopmentPerformanceConfig)
    
    # 配置代理支持
    app.wsgi_app = ProxyFix(
        app.wsgi_app,
        x_for=1,
        x_proto=1,
        x_host=1,
        x_prefix=1
    )
    
    # 禁用不必要的功能
    app.config['PROPAGATE_EXCEPTIONS'] = True
    
    return app

def main():
    """主函数"""
    print("正在启动高性能模式...")
    
    # 创建优化的应用
    app = create_optimized_app()
    
    print("应用配置:")
    print(f"  调试模式: {app.config.get('DEBUG', False)}")
    print(f"  数据库连接池大小: {app.config.get('SQLALCHEMY_ENGINE_OPTIONS', {}).get('pool_size', 'N/A')}")
    print(f"  缓存类型: {app.config.get('CACHE_TYPE', 'N/A')}")
    print(f"  日志级别: {app.config.get('LOG_LEVEL', 'N/A')}")
    
    print("\n启动服务器...")
    print("访问地址: http://localhost:8080")
    print("按 Ctrl+C 停止服务器")
    
    try:
        # 启动服务器 - 高性能配置
        app.run(
            debug=False,              # 关闭调试模式
            host='0.0.0.0',          # 允许外部访问
            port=8080,               # 端口
            use_reloader=False,      # 关闭重载器
            use_debugger=False,      # 关闭调试器
            threaded=True,           # 启用多线程
            processes=1,             # 单进程
            passthrough_errors=False # 不传递错误
        )
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"启动失败: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
