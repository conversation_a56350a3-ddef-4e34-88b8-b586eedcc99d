# =============================================
# 财务系统权限定义
# 需要添加到 app/utils/permissions.py 中的 PERMISSIONS 字典
# =============================================

# 财务模块权限定义
FINANCIAL_PERMISSIONS = {
    # 财务管理
    '财务管理': {
        'name': '财务管理',
        'actions': {
            'view': '查看财务',
            'create': '创建财务记录',
            'edit': '编辑财务记录',
            'delete': '删除财务记录',
            'approve': '审批财务',
            'audit': '财务审计'
        }
    },
    
    # 会计科目管理
    '会计科目管理': {
        'name': '会计科目管理',
        'actions': {
            'view': '查看会计科目',
            'create': '创建会计科目',
            'edit': '编辑会计科目',
            'delete': '删除会计科目'
        }
    },
    
    # 财务凭证管理
    '财务凭证管理': {
        'name': '财务凭证管理',
        'actions': {
            'view': '查看财务凭证',
            'create': '创建财务凭证',
            'edit': '编辑财务凭证',
            'delete': '删除财务凭证',
            'review': '审核财务凭证',
            'post': '记账'
        }
    },
    
    # 应付账款管理
    '应付账款管理': {
        'name': '应付账款管理',
        'actions': {
            'view': '查看应付账款',
            'create': '创建应付账款',
            'edit': '编辑应付账款',
            'delete': '删除应付账款',
            'payment': '付款',
            'approve': '审批付款'
        }
    },
    
    # 收入管理
    '收入管理': {
        'name': '收入管理',
        'actions': {
            'view': '查看收入',
            'create': '创建收入记录',
            'edit': '编辑收入记录',
            'delete': '删除收入记录',
            'review': '审核收入'
        }
    },
    
    # 成本核算
    '成本核算': {
        'name': '成本核算',
        'actions': {
            'view': '查看成本',
            'calculate': '成本核算',
            'review': '审核成本',
            'adjust': '成本调整'
        }
    },
    
    # 财务报表
    '财务报表': {
        'name': '财务报表',
        'actions': {
            'view': '查看报表',
            'export': '导出报表',
            'print': '打印报表',
            'config': '配置报表'
        }
    }
}

# 使用说明：
# 1. 将上述 FINANCIAL_PERMISSIONS 字典中的内容添加到 app/utils/permissions.py 文件的 PERMISSIONS 字典中
# 2. 或者直接在 permissions.py 文件末尾添加：
#    PERMISSIONS.update(FINANCIAL_PERMISSIONS)

# 示例代码片段（添加到 app/utils/permissions.py）：
"""
# 在 PERMISSIONS 字典中添加财务模块
PERMISSIONS.update({
    # 财务管理
    '财务管理': {
        'name': '财务管理',
        'actions': {
            'view': '查看财务',
            'create': '创建财务记录',
            'edit': '编辑财务记录',
            'delete': '删除财务记录',
            'approve': '审批财务',
            'audit': '财务审计'
        }
    },
    
    # 会计科目管理
    '会计科目管理': {
        'name': '会计科目管理',
        'actions': {
            'view': '查看会计科目',
            'create': '创建会计科目',
            'edit': '编辑会计科目',
            'delete': '删除会计科目'
        }
    },
    
    # 财务凭证管理
    '财务凭证管理': {
        'name': '财务凭证管理',
        'actions': {
            'view': '查看财务凭证',
            'create': '创建财务凭证',
            'edit': '编辑财务凭证',
            'delete': '删除财务凭证',
            'review': '审核财务凭证',
            'post': '记账'
        }
    },
    
    # 应付账款管理
    '应付账款管理': {
        'name': '应付账款管理',
        'actions': {
            'view': '查看应付账款',
            'create': '创建应付账款',
            'edit': '编辑应付账款',
            'delete': '删除应付账款',
            'payment': '付款',
            'approve': '审批付款'
        }
    },
    
    # 收入管理
    '收入管理': {
        'name': '收入管理',
        'actions': {
            'view': '查看收入',
            'create': '创建收入记录',
            'edit': '编辑收入记录',
            'delete': '删除收入记录',
            'review': '审核收入'
        }
    },
    
    # 成本核算
    '成本核算': {
        'name': '成本核算',
        'actions': {
            'view': '查看成本',
            'calculate': '成本核算',
            'review': '审核成本',
            'adjust': '成本调整'
        }
    },
    
    # 财务报表
    '财务报表': {
        'name': '财务报表',
        'actions': {
            'view': '查看报表',
            'export': '导出报表',
            'print': '打印报表',
            'config': '配置报表'
        }
    }
})
"""

# 权限检查示例：
"""
# 在路由中使用权限检查
from app.utils.permissions import check_permission

@app.route('/financial/vouchers')
@login_required
@check_permission('财务凭证管理', 'view')
def financial_vouchers():
    # 财务凭证列表页面
    pass

@app.route('/financial/vouchers/create')
@login_required
@check_permission('财务凭证管理', 'create')
def create_financial_voucher():
    # 创建财务凭证页面
    pass
"""
