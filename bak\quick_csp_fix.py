#!/usr/bin/env python3
"""
快速CSP修复工具
临时解决CSP内联事件处理器问题的简单脚本
"""

import os
import re
from pathlib import Path

def quick_fix_csp():
    """快速修复CSP配置，允许内联事件处理器"""
    
    print("🔧 快速CSP修复工具")
    print("=" * 40)
    
    # 修复app/__init__.py中的CSP配置
    init_file = Path("app/__init__.py")
    
    if not init_file.exists():
        print("❌ 找不到 app/__init__.py 文件")
        return False
    
    try:
        # 读取文件内容
        with open(init_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否已经包含 'unsafe-inline'
        if "'unsafe-inline'" in content and "script-src 'self' 'nonce-" in content:
            print("✅ CSP配置已经包含 'unsafe-inline'，无需修复")
            return True
        
        # 查找并替换CSP配置
        # 查找script-src配置行
        pattern = r"(script-src 'self' 'nonce-\{nonce\}')( 'unsafe-hashes' https: http:;)"
        replacement = r"\1 'unsafe-inline'\2"
        
        new_content = re.sub(pattern, replacement, content)
        
        if new_content != content:
            # 创建备份
            backup_file = init_file.with_suffix('.py.backup')
            with open(backup_file, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"📦 创建备份: {backup_file}")
            
            # 写入修复后的内容
            with open(init_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("✅ CSP配置已修复，添加了 'unsafe-inline' 支持")
            print("💡 请重启应用程序以应用更改")
            return True
        else:
            print("⚠️  未找到需要修复的CSP配置")
            return False
            
    except Exception as e:
        print(f"❌ 修复失败: {str(e)}")
        return False

def check_universal_handler():
    """检查通用事件处理器文件是否存在"""
    handler_file = Path("app/static/js/universal-event-handler.js")
    
    if handler_file.exists():
        print("✅ 通用事件处理器文件存在")
        return True
    else:
        print("⚠️  通用事件处理器文件不存在")
        print(f"   请确保 {handler_file} 文件存在")
        return False

def create_simple_universal_handler():
    """创建简单的通用事件处理器"""
    handler_file = Path("app/static/js/universal-event-handler.js")
    
    if handler_file.exists():
        print("✅ 通用事件处理器文件已存在")
        return True
    
    # 确保目录存在
    handler_file.parent.mkdir(parents=True, exist_ok=True)
    
    # 创建简单的事件处理器
    handler_content = '''/**
 * 通用事件处理器
 * 处理 data-onclick 等属性的事件绑定
 */

document.addEventListener('DOMContentLoaded', function() {
    console.log('🔧 通用事件处理器已加载');
    
    // 处理 data-onclick 事件
    document.querySelectorAll('[data-onclick]').forEach(function(element) {
        const funcCall = element.getAttribute('data-onclick');
        element.addEventListener('click', function(e) {
            e.preventDefault();
            try {
                // 安全地执行函数调用
                new Function(funcCall)();
            } catch (error) {
                console.error('事件处理器执行失败:', error, '函数:', funcCall);
            }
        });
    });
    
    // 处理其他事件类型
    const eventTypes = ['change', 'submit', 'focus', 'blur', 'mouseover', 'mouseout'];
    eventTypes.forEach(function(eventType) {
        const selector = '[data-on' + eventType + ']';
        document.querySelectorAll(selector).forEach(function(element) {
            const funcCall = element.getAttribute('data-on' + eventType);
            element.addEventListener(eventType, function(e) {
                try {
                    new Function(funcCall)();
                } catch (error) {
                    console.error('事件处理器执行失败:', error, '函数:', funcCall);
                }
            });
        });
    });
});'''
    
    try:
        with open(handler_file, 'w', encoding='utf-8') as f:
            f.write(handler_content)
        print(f"✅ 创建通用事件处理器: {handler_file}")
        return True
    except Exception as e:
        print(f"❌ 创建通用事件处理器失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("🚀 开始快速CSP修复...")
    print()
    
    success = True
    
    # 1. 修复CSP配置
    if not quick_fix_csp():
        success = False
    
    print()
    
    # 2. 检查/创建通用事件处理器
    if not check_universal_handler():
        if not create_simple_universal_handler():
            success = False
    
    print()
    print("=" * 40)
    
    if success:
        print("🎉 快速修复完成!")
        print()
        print("📋 下一步:")
        print("  1. 重启Flask应用程序")
        print("  2. 刷新浏览器页面")
        print("  3. 检查浏览器控制台是否还有CSP错误")
        print()
        print("💡 如果仍有问题，可能需要:")
        print("  - 将内联事件处理器转换为data-onclick属性")
        print("  - 使用完整的CSP修复工具")
    else:
        print("❌ 修复过程中遇到问题")
        print("   请检查错误信息并手动修复")

if __name__ == "__main__":
    main()
