{% extends 'base.html' %}
{% set page_type = 'list' %}

{% block title %}仓库管理{% endblock %}

{% from '_formhelpers.html' import page_container, card_layout, search_form, table_layout, status_badge, action_buttons, empty_state %}
{% from '_pagination.html' import render_pagination %}

{% block content %}
{% call page_container() %}
    {% call card_layout('仓库列表', '管理学校仓库信息，包括仓库基本信息、容量和状态', '<a href="' + url_for('warehouse.create') + '" class="btn btn-primary btn-sm"><i class="fas fa-plus"></i> 创建仓库</a>') %}

        <!-- 搜索表单 -->
        {% call search_form(url_for('warehouse.index')) %}
            <div class="col-md-5">
                <div class="form-group">
                    <label>区域</label>
                    <select name="area_id" class="form-control">
                        <option value="">全部</option>
                        {% for area in areas %}
                        <option value="{{ area.id }}" {% if area_id == area.id %}selected{% endif %}>{{ area.name }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="col-md-5">
                <div class="form-group">
                    <label>状态</label>
                    <select name="status" class="form-control">
                        <option value="">全部</option>
                        <option value="正常" {% if status == '正常' %}selected{% endif %}>正常</option>
                        <option value="维护中" {% if status == '维护中' %}selected{% endif %}>维护中</option>
                        <option value="已关闭" {% if status == '已关闭' %}selected{% endif %}>已关闭</option>
                    </select>
                </div>
            </div>
        {% endcall %}

        <!-- 仓库列表 -->
        {% if warehouses %}
            {% call table_layout(['名称', '区域', '位置', '管理员', '容量', '状态', '操作']) %}
                {% for warehouse in warehouses %}
                <tr>
                    <td>{{ warehouse.name }}</td>
                    <td>{{ warehouse.area.name }}</td>
                    <td>{{ warehouse.location }}</td>
                    <td>{{ warehouse.manager.real_name or warehouse.manager.username }}</td>
                    <td>{{ warehouse.capacity }} {{ warehouse.capacity_unit }}</td>
                    <td>{{ status_badge(warehouse.status) }}</td>
                    <td>
                        {{ action_buttons([
                            {
                                'url': url_for('warehouse.view', id=warehouse.id),
                                'type': 'info',
                                'icon': 'fas fa-eye',
                                'text': '查看',
                                'title': '查看仓库详情'
                            },
                            {
                                'url': url_for('warehouse.edit', id=warehouse.id),
                                'type': 'primary',
                                'icon': 'fas fa-edit',
                                'text': '编辑',
                                'title': '编辑仓库信息'
                            }
                        ]) }}
                    </td>
                </tr>
                {% endfor %}
            {% endcall %}
        {% else %}
            {{ empty_state('暂无仓库数据', 'fas fa-warehouse', '创建仓库', url_for('warehouse.create')) }}
        {% endif %}

        <!-- 分页 -->
        {{ render_pagination(pagination, 'warehouse.index', area_id=area_id, status=status) }}

    {% endcall %}
{% endcall %}
{% endblock %}
