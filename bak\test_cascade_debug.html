<!DOCTYPE html>
<html>
<head>
    <title>测试分级联动选择</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>测试分级联动选择API</h1>
    
    <div>
        <h3>1. 测试科目类型API</h3>
        <button onclick="testTypes()">测试科目类型</button>
        <div id="types-result"></div>
    </div>
    
    <div>
        <h3>2. 测试一级科目API</h3>
        <select id="type-select" onchange="testByType()">
            <option value="">选择科目类型</option>
            <option value="资产">资产</option>
            <option value="负债">负债</option>
            <option value="收入">收入</option>
            <option value="费用">费用</option>
        </select>
        <div id="by-type-result"></div>
    </div>
    
    <div>
        <h3>3. 测试下级科目API</h3>
        <input type="number" id="parent-id" placeholder="输入上级科目ID">
        <button onclick="testByParent()">测试下级科目</button>
        <div id="by-parent-result"></div>
    </div>

    <script>
        function testTypes() {
            console.log('测试科目类型API...');
            $.ajax({
                url: '/financial/accounting-subjects/types',
                method: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log('科目类型API成功:', data);
                    $('#types-result').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('科目类型API失败:', xhr.status, error);
                    $('#types-result').html('<span style="color:red">错误: ' + xhr.status + ' - ' + error + '</span>');
                }
            });
        }
        
        function testByType() {
            const subjectType = $('#type-select').val();
            if (!subjectType) return;
            
            console.log('测试一级科目API，类型:', subjectType);
            $.ajax({
                url: '/financial/accounting-subjects/by-type',
                method: 'GET',
                data: { subject_type: subjectType },
                dataType: 'json',
                success: function(data) {
                    console.log('一级科目API成功:', data);
                    $('#by-type-result').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('一级科目API失败:', xhr.status, error);
                    $('#by-type-result').html('<span style="color:red">错误: ' + xhr.status + ' - ' + error + '</span>');
                }
            });
        }
        
        function testByParent() {
            const parentId = $('#parent-id').val();
            if (!parentId) return;
            
            console.log('测试下级科目API，上级ID:', parentId);
            $.ajax({
                url: '/financial/accounting-subjects/by-parent',
                method: 'GET',
                data: { parent_id: parentId },
                dataType: 'json',
                success: function(data) {
                    console.log('下级科目API成功:', data);
                    $('#by-parent-result').html('<pre>' + JSON.stringify(data, null, 2) + '</pre>');
                },
                error: function(xhr, status, error) {
                    console.error('下级科目API失败:', xhr.status, error);
                    $('#by-parent-result').html('<span style="color:red">错误: ' + xhr.status + ' - ' + error + '</span>');
                }
            });
        }
        
        // 页面加载时自动测试科目类型
        $(document).ready(function() {
            testTypes();
        });
    </script>
</body>
</html>
