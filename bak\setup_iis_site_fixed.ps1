# PowerShell脚本：配置IIS站点
# 需要以管理员身份运行

# 确保IIS模块正确加载
try {
    Import-Module WebAdministration -Force
    Write-Host "WebAdministration模块加载成功" -ForegroundColor Green
} catch {
    Write-Host "WebAdministration模块加载失败，尝试启用IIS功能..." -ForegroundColor Yellow
    Enable-WindowsOptionalFeature -Online -FeatureName IIS-WebServerRole, IIS-WebServer, IIS-CommonHttpFeatures, IIS-HttpErrors, IIS-HttpLogging, IIS-RequestFiltering, IIS-StaticContent, IIS-DefaultDocument, IIS-DirectoryBrowsing -All
    Import-Module WebAdministration -Force
}

Write-Host "正在配置IIS站点..." -ForegroundColor Green

# 站点配置参数
$siteName = "xiaoyuanst.com"
$sitePort = 80
$sitePath = "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP"
$appPoolName = "xiaoyuanst.com_AppPool"

try {
    # 1. 创建应用程序池
    Write-Host "创建应用程序池: $appPoolName" -ForegroundColor Yellow
    
    # 检查应用程序池是否存在
    $existingPool = Get-ChildItem IIS:\AppPools | Where-Object { $_.Name -eq $appPoolName }
    if ($existingPool) {
        Remove-WebAppPool -Name $appPoolName
        Write-Host "已删除现有应用程序池" -ForegroundColor Yellow
    }
    
    New-WebAppPool -Name $appPoolName
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "processModel.identityType" -Value "ApplicationPoolIdentity"
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "managedRuntimeVersion" -Value ""
    Set-ItemProperty -Path "IIS:\AppPools\$appPoolName" -Name "enable32BitAppOnWin64" -Value $false
    
    Write-Host "应用程序池创建完成" -ForegroundColor Green
    
    # 2. 删除默认网站（如果存在）
    if (Get-Website -Name "Default Web Site" -ErrorAction SilentlyContinue) {
        Remove-Website -Name "Default Web Site"
        Write-Host "已删除默认网站" -ForegroundColor Yellow
    }
    
    # 3. 删除现有站点（如果存在）
    if (Get-Website -Name $siteName -ErrorAction SilentlyContinue) {
        Remove-Website -Name $siteName
        Write-Host "已删除现有站点" -ForegroundColor Yellow
    }
    
    # 4. 创建新站点
    Write-Host "创建站点: $siteName" -ForegroundColor Yellow
    New-Website -Name $siteName -Port $sitePort -PhysicalPath $sitePath -ApplicationPool $appPoolName
    
    # 5. 添加域名绑定
    Write-Host "添加域名绑定..." -ForegroundColor Yellow
    New-WebBinding -Name $siteName -IPAddress "*" -Port 80 -HostHeader "xiaoyuanst.com"
    New-WebBinding -Name $siteName -IPAddress "*" -Port 80 -HostHeader "www.xiaoyuanst.com"
    
    # 6. 设置站点权限
    Write-Host "设置站点权限..." -ForegroundColor Yellow
    $acl = Get-Acl $sitePath
    $accessRule = New-Object System.Security.AccessControl.FileSystemAccessRule("IIS_IUSRS", "FullControl", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule)
    $accessRule2 = New-Object System.Security.AccessControl.FileSystemAccessRule("IUSR", "ReadAndExecute", "ContainerInherit,ObjectInherit", "None", "Allow")
    $acl.SetAccessRule($accessRule2)
    Set-Acl -Path $sitePath -AclObject $acl
    
    # 7. 启动站点和应用程序池
    Write-Host "启动站点和应用程序池..." -ForegroundColor Yellow
    Start-WebAppPool -Name $appPoolName
    Start-Website -Name $siteName
    
    Write-Host "IIS站点配置完成！" -ForegroundColor Green
    Write-Host "站点名称: $siteName" -ForegroundColor Cyan
    Write-Host "端口: $sitePort" -ForegroundColor Cyan
    Write-Host "物理路径: $sitePath" -ForegroundColor Cyan
    Write-Host "应用程序池: $appPoolName" -ForegroundColor Cyan
    Write-Host "域名绑定: xiaoyuanst.com, www.xiaoyuanst.com" -ForegroundColor Cyan
    
} catch {
    Write-Host "配置过程中出现错误: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "请确保:" -ForegroundColor Yellow
Write-Host "1. Flask应用正在端口8080上运行" -ForegroundColor White
Write-Host "2. 域名DNS记录指向此服务器" -ForegroundColor White
Write-Host "3. 防火墙允许端口80访问" -ForegroundColor White
