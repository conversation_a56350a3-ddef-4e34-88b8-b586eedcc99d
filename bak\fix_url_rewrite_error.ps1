# PowerShell脚本：修复IIS URL Rewrite模块错误
# 解决 HTTP 错误 500.50 - URL Rewrite Module Error
# 需要以管理员身份运行

Write-Host "=== 修复IIS URL Rewrite模块错误 ===" -ForegroundColor Green
Write-Host "错误: HTTP 错误 500.50 - URL Rewrite Module Error" -ForegroundColor Yellow
Write-Host "原因: 服务器变量 HTTP_X_FORWARDED_FOR 不被允许设置" -ForegroundColor Yellow

try {
    Import-Module WebAdministration -Force
    Write-Host "✓ WebAdministration模块加载成功" -ForegroundColor Green
} catch {
    Write-Host "✗ WebAdministration模块加载失败" -ForegroundColor Red
    exit 1
}

Write-Host "`n=== 第一步：检查URL Rewrite模块 ===" -ForegroundColor Yellow

# 检查URL Rewrite模块是否安装
$urlRewriteModule = Get-WebGlobalModule -Name "RewriteModule" -ErrorAction SilentlyContinue
if ($urlRewriteModule) {
    Write-Host "✓ URL Rewrite模块已安装" -ForegroundColor Green
} else {
    Write-Host "✗ URL Rewrite模块未安装，正在安装..." -ForegroundColor Red
    
    # 下载并安装URL Rewrite模块
    $url = "https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi"
    $output = "$env:TEMP\rewrite_amd64_en-US.msi"
    
    try {
        Invoke-WebRequest -Uri $url -OutFile $output
        Start-Process msiexec.exe -Wait -ArgumentList "/i $output /quiet"
        Remove-Item $output -Force
        Write-Host "✓ URL Rewrite模块安装完成" -ForegroundColor Green
    } catch {
        Write-Host "✗ URL Rewrite模块安装失败: $($_.Exception.Message)" -ForegroundColor Red
        exit 1
    }
}

Write-Host "`n=== 第二步：配置允许的服务器变量 ===" -ForegroundColor Yellow

# 站点名称
$siteName = "xiaoyuanst.com"

# 检查站点是否存在
$site = Get-Website -Name $siteName -ErrorAction SilentlyContinue
if (-not $site) {
    Write-Host "✗ 站点 $siteName 不存在，请先运行 setup_xiaoyuanst_domain.ps1" -ForegroundColor Red
    exit 1
}

Write-Host "✓ 找到站点: $siteName" -ForegroundColor Green

# 配置允许的服务器变量
try {
    # 使用PowerShell命令配置允许的服务器变量
    $serverVariables = @(
        "HTTP_X_FORWARDED_FOR",
        "HTTP_X_FORWARDED_PROTO", 
        "HTTP_X_FORWARDED_HOST",
        "HTTP_X_REAL_IP"
    )
    
    foreach ($variable in $serverVariables) {
        try {
            # 添加允许的服务器变量
            Add-WebConfigurationProperty -PSPath "IIS:\Sites\$siteName" -Filter "system.webServer/rewrite/allowedServerVariables" -Name "." -Value @{name=$variable}
            Write-Host "✓ 已添加允许的服务器变量: $variable" -ForegroundColor Green
        } catch {
            # 如果变量已存在，会抛出异常，这是正常的
            Write-Host "- 服务器变量已存在: $variable" -ForegroundColor Gray
        }
    }
    
} catch {
    Write-Host "✗ 配置服务器变量时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 第三步：验证web.config文件 ===" -ForegroundColor Yellow

$webConfigPath = "C:\StudentsCMSSP\web.config"
if (Test-Path $webConfigPath) {
    Write-Host "✓ web.config文件存在" -ForegroundColor Green
    
    # 检查web.config内容
    $webConfigContent = Get-Content $webConfigPath -Raw
    
    if ($webConfigContent -match "allowedServerVariables") {
        Write-Host "✓ web.config包含allowedServerVariables配置" -ForegroundColor Green
    } else {
        Write-Host "✗ web.config缺少allowedServerVariables配置" -ForegroundColor Red
        Write-Host "正在更新web.config文件..." -ForegroundColor Yellow
        
        # 备份原文件
        Copy-Item $webConfigPath "$webConfigPath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
        Write-Host "✓ 已备份原web.config文件" -ForegroundColor Green
        
        # 这里可以添加更新web.config的逻辑
        Write-Host "请手动检查web.config文件是否包含正确的allowedServerVariables配置" -ForegroundColor Yellow
    }
    
    if ($webConfigContent -match "127\.0\.0\.1:8080") {
        Write-Host "✓ web.config包含正确的代理配置（端口8080）" -ForegroundColor Green
    } else {
        Write-Host "✗ web.config代理配置可能不正确" -ForegroundColor Red
    }
} else {
    Write-Host "✗ web.config文件不存在于: $webConfigPath" -ForegroundColor Red
}

Write-Host "`n=== 第四步：重启IIS服务 ===" -ForegroundColor Yellow

try {
    # 重启应用程序池
    $appPoolName = "xiaoyuanst.com_AppPool"
    if (Get-WebAppPool -Name $appPoolName -ErrorAction SilentlyContinue) {
        Restart-WebAppPool -Name $appPoolName
        Write-Host "✓ 已重启应用程序池: $appPoolName" -ForegroundColor Green
    }
    
    # 重启站点
    Stop-Website -Name $siteName
    Start-Website -Name $siteName
    Write-Host "✓ 已重启站点: $siteName" -ForegroundColor Green
    
    # 重启IIS
    Write-Host "正在重启IIS服务..." -ForegroundColor Yellow
    iisreset /noforce
    Write-Host "✓ IIS服务重启完成" -ForegroundColor Green
    
} catch {
    Write-Host "✗ 重启服务时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n=== 第五步：测试修复结果 ===" -ForegroundColor Yellow

# 等待服务启动
Start-Sleep -Seconds 5

try {
    # 测试本地访问
    Write-Host "测试本地访问..." -ForegroundColor White
    $response = Invoke-WebRequest -Uri "http://localhost" -TimeoutSec 10 -ErrorAction SilentlyContinue
    
    if ($response.StatusCode -eq 200) {
        Write-Host "✓ 本地访问测试成功（HTTP 200）" -ForegroundColor Green
    } else {
        Write-Host "✗ 本地访问测试失败，状态码: $($response.StatusCode)" -ForegroundColor Red
    }
} catch {
    Write-Host "✗ 本地访问测试失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 检查具体错误
    if ($_.Exception.Message -match "500") {
        Write-Host "仍然存在500错误，请检查以下内容：" -ForegroundColor Yellow
        Write-Host "1. Flask应用是否在端口8080上运行" -ForegroundColor White
        Write-Host "2. web.config文件配置是否正确" -ForegroundColor White
        Write-Host "3. IIS日志文件中的详细错误信息" -ForegroundColor White
    }
}

Write-Host "`n=== 修复完成 ===" -ForegroundColor Green
Write-Host "如果问题仍然存在，请检查：" -ForegroundColor Yellow
Write-Host "1. 确保Flask应用正在运行：python run.py" -ForegroundColor White
Write-Host "2. 检查IIS日志：C:\inetpub\logs\LogFiles\" -ForegroundColor White
Write-Host "3. 检查Windows事件日志" -ForegroundColor White
Write-Host "4. 运行测试脚本：.\test_xiaoyuanst_domain.ps1" -ForegroundColor White

Write-Host "`n访问地址：" -ForegroundColor Cyan
Write-Host "- 本地访问: http://localhost" -ForegroundColor White
Write-Host "- IP访问: http://**************" -ForegroundColor White
Write-Host "- 域名访问: http://xiaoyuanst.com" -ForegroundColor White
