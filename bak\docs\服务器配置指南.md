# Flask应用服务器配置指南

## 服务器信息
- 域名：xiaoyuanst.com
- IP地址：**************
- Flask端口：8080
- IIS端口：80

## 当前问题
- Flask应用配置为80端口，但被IIS占用
- 需要配置IIS作为反向代理，实现外网访问

## 解决方案：IIS反向代理配置

### 快速配置（推荐）

使用自动化脚本进行配置：

```powershell
# 以管理员身份运行PowerShell，然后执行：
.\setup_xiaoyuanst_domain.ps1
```

### 手动配置步骤

#### 第一步：配置IIS站点

以管理员身份打开PowerShell，逐条执行以下命令：

```powershell
# 1. 导入IIS管理模块
Import-Module WebAdministration

# 2. 删除默认网站（避免端口冲突）
Remove-Website -Name "Default Web Site" -ErrorAction SilentlyContinue

# 3. 创建新的应用程序池
New-WebAppPool -Name "tdtech_pool" -Force
Set-ItemProperty -Path "IIS:\AppPools\tdtech_pool" -Name "managedRuntimeVersion" -Value ""

# 4. 创建网站
New-Website -Name "tdtech" -Port 80 -PhysicalPath "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP" -ApplicationPool "tdtech_pool"

# 5. 启动应用程序池和网站
Start-WebAppPool -Name "tdtech_pool"
Start-Website -Name "tdtech"
```

### 第二步：安装URL Rewrite模块

```powershell
# 下载并安装URL Rewrite模块
$url = "https://download.microsoft.com/download/1/2/8/128E2E22-C1B9-44A4-BE2A-5859ED1D4592/rewrite_amd64_en-US.msi"
$output = "$env:TEMP\rewrite_amd64_en-US.msi"
Invoke-WebRequest -Uri $url -OutFile $output
Start-Process msiexec.exe -Wait -ArgumentList "/i $output /quiet"
Remove-Item $output -Force

# 重启IIS
iisreset
```

### 第三步：验证配置文件

确认项目根目录下的 `web.config` 文件内容正确：

```xml
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <system.webServer>
        <!-- URL重写规则 - 反向代理到Flask应用 -->
        <rewrite>
            <rules>
                <rule name="ReverseProxyInboundRule1" stopProcessing="true">
                    <match url="(.*)" />
                    <action type="Rewrite" url="http://127.0.0.1:8080/{R:1}" />
                    <serverVariables>
                        <set name="HTTP_X_FORWARDED_FOR" value="{REMOTE_ADDR}" />
                        <set name="HTTP_X_FORWARDED_PROTO" value="http" />
                        <set name="HTTP_X_FORWARDED_HOST" value="{HTTP_HOST}" />
                    </serverVariables>
                </rule>
            </rules>
        </rewrite>
        <httpErrors errorMode="Detailed" />
    </system.webServer>
</configuration>
```

### 第四步：启动Flask应用

确认 `run.py` 配置正确（应该是127.0.0.1:8080）：

```python
if __name__ == '__main__':
    # 绑定到8080端口，通过IIS代理对外提供服务
    app.run(debug=1, host='127.0.0.1', port=8080, use_reloader=False, threaded=True)
```

然后启动Flask应用：

```powershell
python run.py
```

### 第五步：测试访问

```powershell
# 测试Flask应用直接访问
curl http://localhost:8080

# 测试IIS代理访问
curl http://localhost

# 测试外网访问
curl http://xiaoyuanst.com
```

## 架构说明

```
外网用户 → IIS (80端口) → Flask应用 (8080端口)
```

- **IIS**: 接收外网请求，处理静态文件，提供SSL支持
- **URL Rewrite**: 将动态请求转发给Flask应用
- **Flask**: 处理业务逻辑，返回动态内容

## 常见问题排查

### 1. 端口被占用
```powershell
netstat -ano | findstr :80
netstat -ano | findstr :8080
```

### 2. IIS服务状态
```powershell
Get-Service W3SVC
```

### 3. 应用程序池状态
```powershell
Get-WebAppPool
```

### 4. 网站状态
```powershell
Get-Website
```

## 防火墙配置

确保防火墙允许80端口访问：

```powershell
New-NetFirewallRule -DisplayName "Allow HTTP" -Direction Inbound -Protocol TCP -LocalPort 80 -Action Allow
```

## 测试配置

使用测试脚本验证配置：

```powershell
# 以管理员身份运行PowerShell，然后执行：
.\test_xiaoyuanst_domain.ps1
```

## 完成后的访问方式

- 本地访问：http://localhost
- IP访问：http://**************
- 域名访问：http://xiaoyuanst.com
- 带www访问：http://www.xiaoyuanst.com
- 直接访问Flask：http://localhost:8080（仅用于调试）
